<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="41dp"
        android:background="@drawable/bottom_sheet_rounded_top_bg"
        android:orientation="vertical"
        android:paddingBottom="26dp">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:background="@drawable/login_dialog_bg" />

        <!-- 昵称和通话时长 -->
        <TextView
            android:id="@+id/tvNickname"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="50dp"
            android:text="Pinkis"
            android:textColor="@color/black"
            android:textSize="15sp"
            android:textStyle="bold" />


        <LinearLayout
            android:id="@+id/line_call_duration"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_below="@+id/tvNickname"
            android:layout_marginTop="6dp"
            android:gravity="center">
            <TextView
                android:id="@+id/tvCallDuration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Call duration: "
                android:textColor="#A3A3A3"
                android:textSize="13sp" />
            <TextView
                android:id="@+id/tvCallDurationTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="5s"
                android:textSize="13sp"
                android:textStyle="bold"
                android:textColor="@color/black"/>


        </LinearLayout>


        <!-- 喜恶选择 -->
        <LinearLayout
            android:id="@+id/like_layout"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_below="@+id/line_call_duration"
            android:layout_marginHorizontal="38dp"
            android:layout_marginTop="16dp"
            android:gravity="center"
            android:orientation="horizontal">

            <com.score.callmetest.ui.widget.AlphaLinearLayout
                android:id="@+id/likeBtn"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/like_img"
                    android:layout_width="23dp"
                    android:layout_height="23dp"
                    android:src="@drawable/ic_like" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:text="Like"
                    android:textColor="@color/black"
                    android:textSize="14sp" />

            </com.score.callmetest.ui.widget.AlphaLinearLayout>

            <Space
                android:layout_width="13dp"
                android:layout_height="1dp" />

            <com.score.callmetest.ui.widget.AlphaLinearLayout
                android:id="@+id/dislikeBtn"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/dislike_img"
                    android:layout_width="23dp"
                    android:layout_height="23dp"
                    android:src="@drawable/ic_dislike" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:text="Dislike"
                    android:textColor="@color/black"
                    android:textSize="14sp" />

            </com.score.callmetest.ui.widget.AlphaLinearLayout>
        </LinearLayout>


        <!-- Impression标题 -->
        <TextView
            android:id="@+id/tvImpressionTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/like_layout"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="18dp"
            android:text="Impression"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:textStyle="bold" />

        <!-- Impression标签区 -->
        <com.google.android.flexbox.FlexboxLayout
            android:id="@+id/recyclerImpression"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tvImpressionTitle"
            android:layout_centerHorizontal="true"
            android:layout_marginHorizontal="24dp"
            android:layout_marginTop="17dp"
            android:minHeight="96dp"
            app:flexWrap="wrap"
            app:flexDirection="row"
            app:justifyContent="flex_start"
            app:alignItems="center" />


        <!-- Next按钮 -->
        <com.score.callmetest.ui.widget.AlphaTextView
            android:id="@+id/nextBtn"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_below="@+id/recyclerImpression"
            android:layout_marginHorizontal="32dp"
            android:layout_marginTop="18dp"
            android:background="@drawable/bg_btn_rounded_blue"
            android:enabled="false"
            android:gravity="center"
            android:text="Next"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold" />
        <LinearLayout
            android:id="@+id/line_recommendTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginTop="18dp"
            android:layout_marginBottom="8dp"
            android:layout_marginHorizontal="41dp"
            android:layout_below="@+id/nextBtn">

            <!-- 左边的线 -->
            <View
                android:layout_width="0dp"
                android:layout_height="0.5dp"
                android:layout_weight="1"
                android:background="@drawable/line_gray" />

            <!-- 推荐区标题 -->
            <TextView
                android:id="@+id/tvRecommendTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="16dp"
                android:text="Other highlights live"
                android:textColor="#959595"
                android:textSize="14sp"
                android:textStyle="bold" />

            <!-- 右边的线 -->
            <View
                android:layout_width="0dp"
                android:layout_height="0.5dp"
                android:layout_weight="1"
                android:background="@drawable/line_gray" />
        </LinearLayout>
        
        <!-- 推荐区头像 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerRecommend"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/line_recommendTitle"
            android:layout_centerHorizontal="true"
            android:layout_marginHorizontal="24dp"
            android:clipToPadding="false"
            android:overScrollMode="never"/>

    </RelativeLayout>

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/bg_avatar_border"
        android:padding="2.5dp">

        <com.score.callmetest.ui.widget.CircleIconButton
            android:id="@+id/avatar"
            android:layout_width="69dp"
            android:layout_height="69dp" />
    </FrameLayout>


</FrameLayout>
