<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="29dp"
        android:background="@drawable/bg_dialog_rounded">

        <ImageView
            android:id="@+id/dialog_bg"
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:background="@drawable/login_dialog_bg" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center_horizontal"
            android:paddingHorizontal="32dp"
            android:paddingTop="73dp"
            android:paddingBottom="25dp">

            <TextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="Give us a good rating on Google Play Store to encourage us to do better"
                android:textColor="@color/black"
                android:textSize="15sp"
                android:textStyle="bold" />

            <RatingBar
                android:id="@+id/rb_rating"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="20dp"
                android:isIndicator="false"
                android:numStars="5"
                android:rating="0"
                android:stepSize="1.0"
                android:progressTint="@color/star_color"
                android:progressBackgroundTint="@color/gray_ccc"
                android:secondaryProgressTint="@color/star_color" />
            
            <com.score.callmetest.ui.widget.AlphaLinearLayout
                android:id="@+id/btn_layout"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="24dp"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/btn_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="Submit"
                    android:textColor="@android:color/white"
                    android:textSize="15sp"
                    android:textStyle="bold" />
            </com.score.callmetest.ui.widget.AlphaLinearLayout>
        </LinearLayout>
    </FrameLayout>

    <ImageView
        android:id="@+id/emoji"
        android:layout_width="wrap_content"
        android:layout_height="91dp"
        android:layout_gravity="center_horizontal"
        android:src="@drawable/emoji_smile" />

</FrameLayout>