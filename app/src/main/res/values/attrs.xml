<resources>
    <declare-styleable name="RoundIconButton">
        <attr name="iconSrc" format="reference" />
        <attr name="bgColor" format="color" />
        <!-- 新增的描边属性 -->
        <attr name="strokeWidth" format="dimension" />
        <attr name="strokeColor" format="color" />
    </declare-styleable>

    <declare-styleable name="MineFunctionItemView">
        <attr name="mfi_icon" format="reference" />
        <attr name="mfi_title" format="string" />
        <attr name="mfi_tip" format="string" />
        <attr name="mfi_show_arrow" format="boolean" />
    </declare-styleable>

    <!-- 用于View扩展函数的ID -->
    <item name="click_time_tag" type="id" />
</resources> 