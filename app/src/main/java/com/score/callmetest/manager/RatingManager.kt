package com.score.callmetest.manager

import com.score.callmetest.CallmeApplication
import com.score.callmetest.ui.rating.RatingDialog
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.SharePreferenceUtil

object RatingManager {
    private const val PAY_SUCCESS_TIMES = "pay_success_times"
    private const val HAS_SHOW_RATING_DIALOG = "has_show_rating_dialog"

    fun addPaySuccessTimes() {
        SharePreferenceUtil.putInt(PAY_SUCCESS_TIMES, SharePreferenceUtil.getInt(PAY_SUCCESS_TIMES, 0) + 1)
        checkShowRatingDialog()
    }

    fun checkShowRatingDialog() {
        val paySuccessTimes = SharePreferenceUtil.getInt(PAY_SUCCESS_TIMES, 0)
        if (paySuccessTimes >= 2) {
            // 显示评分对话框
            if (!SharePreferenceUtil.getBoolean(HAS_SHOW_RATING_DIALOG, false)) {
                ActivityUtils.getTopActivity()?.let {
                    RatingDialog(it).show()
                    SharePreferenceUtil.putBoolean(HAS_SHOW_RATING_DIALOG, true)
                }
            }
        }
    }
}