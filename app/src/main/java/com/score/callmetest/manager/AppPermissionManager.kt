package com.score.callmetest.manager

import android.Manifest
import android.app.Activity
import android.app.AlertDialog
import android.content.Context
import android.os.Build
import android.os.Handler
import android.os.Looper
import androidx.appcompat.app.AppCompatActivity
import com.score.callmetest.util.PermissionUtils
import com.score.callmetest.util.PermissionUtils.PermissionCallback
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.ToastUtils
import timber.log.Timber

/**
 * 应用权限管理器
 * 负责处理应用权限的检查和请求
 */
object AppPermissionManager {
    private const val TAG = "AppPermissionManager"
    // 权限相关常量
    private const val KEY_FIRST_LAUNCH = "key_first_launch"
    private const val KEY_PERMISSION_DIALOG_SHOWN = "key_permission_dialog_shown"



    // 权限定义
    private val CAMERA_PERMISSIONS = arrayOf(Manifest.permission.CAMERA)
    private val MICROPHONE_PERMISSIONS = arrayOf(Manifest.permission.RECORD_AUDIO)
    private val STORAGE_PERMISSIONS = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        arrayOf(Manifest.permission.READ_MEDIA_IMAGES)
    } else {
        arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE)
    }

    // 请求码
    private const val REQUEST_CODE_CAMERA = 1001
    private const val REQUEST_CODE_MICROPHONE = 1002
    private const val REQUEST_CODE_STORAGE = 1003
    private const val REQUEST_CODE_CAMERA_MICROPHONE = 1004

    /**
     * 检查是否是首次启动
     */
    fun isFirstLaunch(context: Context): Boolean {
        return SharePreferenceUtil.getBoolean(KEY_FIRST_LAUNCH, true)
    }

    /**
     * 标记非首次启动
     */
    fun markNotFirstLaunch(context: Context) {
        SharePreferenceUtil.putBoolean(KEY_FIRST_LAUNCH, false)
    }

    /**
     * 检查是否已显示过权限弹窗
     */
    fun hasShownPermissionDialog(context: Context): Boolean {
        return SharePreferenceUtil.getBoolean(KEY_PERMISSION_DIALOG_SHOWN, false)
    }

    /**
     * 标记已显示权限弹窗
     */
    fun markPermissionDialogShown(context: Context) {
        SharePreferenceUtil.putBoolean(KEY_PERMISSION_DIALOG_SHOWN, true)
    }

    /**
     * 检查是否需要显示首次启动权限弹窗
     */
    fun shouldShowFirstLaunchPermissionDialog(context: Context): Boolean {
        return false
        // 不显示
//        return isFirstLaunch(context) && !hasShownPermissionDialog(context)
    }

    /**
     * 显示首次启动权限弹窗
     */
    fun showFirstLaunchPermissionDialog(
        activity: AppCompatActivity,
        onGranted: () -> Unit = {},
        onDenied: () -> Unit = {}
    ) {
        if (!shouldShowFirstLaunchPermissionDialog(activity)) {
            onGranted()
            return
        }

        val dialog = AlertDialog.Builder(activity)
            .setTitle("📱 Permission Request")
            .setMessage(
                "To provide you with a better service experience, we need the following permissions:\n\n" +
                        "📷 Album Permission\n" +
                        "   For uploading and modifying avatars, making your profile more complete\n\n" +
                        "📹 Camera Permission\n" +
                        "   For video calls, face-to-face communication with friends\n\n" +
                        "🎤 Microphone Permission\n" +
                        "   For voice calls, ensuring clear call quality\n\n" +
                        "💡 Reminder: You can modify these permissions in settings at any time"
            )
            .setPositiveButton("✅ Grant Now") { _, _ ->
                markPermissionDialogShown(activity)
                requestAllPermissions(activity, onGranted, onDenied)
            }
            .setNegativeButton("⏰ Later") { _, _ ->
                markPermissionDialogShown(activity)
                onDenied()
            }
            .setCancelable(false)
            .create()

        dialog.show()
    }

    /**
     * 请求所有权限
     */
    private fun requestAllPermissions(
        activity: AppCompatActivity,
        onGranted: () -> Unit,
        onDenied: () -> Unit
    ) {
        if (PermissionUtils.hasAllPermissions(activity, CAMERA_PERMISSIONS)) {
            onGranted()
            return
        }

        // 直接请求权限
        requestCameraPermission(activity, onGranted, onDenied)
    }

    /**
     * 检查麦克风权限
     */
    fun checkAndRequestMicrophonePermission(
        activity: AppCompatActivity,
        onGranted: () -> Unit,
        onDenied: () -> Unit
    ) {
        if (PermissionUtils.hasAllPermissions(activity, MICROPHONE_PERMISSIONS)) {
            onGranted()
            return
        }

        // 直接请求权限
        requestMicrophonePermission(activity, onGranted, onDenied)
    }

    /**
     * 检查相册权限（用于上传头像或修改头像时）
     */
    fun checkAndRequestStoragePermission(
        activity: AppCompatActivity,
        onGranted: () -> Unit,
        onDenied: () -> Unit
    ) {
        if (PermissionUtils.hasAllPermissions(activity, STORAGE_PERMISSIONS)) {
            onGranted()
            return
        }

        // 直接请求权限
        requestStoragePermission(activity, onGranted, onDenied)
    }

    /**
     * 检查摄像头和麦克风权限（用于拨打或接听电话时）
     */
    fun checkAndRequestCameraMicrophonePermission(
        activity: AppCompatActivity,
        onGranted: () -> Unit = {},
        onDenied: () -> Unit = {}
    ) {
        val allPermissions = CAMERA_PERMISSIONS + MICROPHONE_PERMISSIONS
        if (PermissionUtils.hasAllPermissions(activity, allPermissions)) {
            onGranted()
            return
        }

        // 直接请求权限
        requestCameraMicrophonePermission(activity, onGranted, onDenied)
    }

    /**
     * 请求摄像头权限
     */
    private fun requestCameraPermission(
        activity: AppCompatActivity,
        onGranted: () -> Unit,
        onDenied: () -> Unit
    ) {
        PermissionUtils.requestPermission(
            activity,
            Manifest.permission.CAMERA,
            REQUEST_CODE_CAMERA,
            object : PermissionCallback {
                override fun onPermissionsGranted(permissions: List<String>) {
                    Timber.tag(TAG).d("Camera permission granted successfully: $permissions")
                    onGranted()
                }

                override fun onPermissionsDenied(
                    deniedPermissions: List<String>,
                    permanentlyDeniedPermissions: List<String>
                ) {
                    Timber.tag(TAG).d("Camera permission denied: $deniedPermissions")
                    onDenied()
                }
            }
        )
    }

    /**
     * 请求麦克风权限
     */
  private fun requestMicrophonePermission(
        activity: AppCompatActivity,
        onGranted: () -> Unit,
        onDenied: () -> Unit
    ) {
        PermissionUtils.requestPermission(
            activity,
            Manifest.permission.RECORD_AUDIO,
            REQUEST_CODE_MICROPHONE,
            object : PermissionCallback {
                override fun onPermissionsGranted(permissions: List<String>) {
                    Timber.tag(TAG).d("Microphone permission granted successfully: $permissions")
                    onGranted()
                }

                override fun onPermissionsDenied(
                    deniedPermissions: List<String>,
                    permanentlyDeniedPermissions: List<String>
                ) {
                    Timber.tag(TAG).d("Microphone permission denied: $deniedPermissions")
                    onDenied()
                }
            }
        )
    }

    /**
     * 请求相册权限
     */
    private fun requestStoragePermission(
        activity: AppCompatActivity,
        onGranted: () -> Unit,
        onDenied: () -> Unit
    ) {
        PermissionUtils.requestPermissions(
            activity,
            STORAGE_PERMISSIONS,
            REQUEST_CODE_STORAGE,
            object : PermissionCallback {
                override fun onPermissionsGranted(permissions: List<String>) {
                    Timber.tag(TAG).d("Storage permission granted successfully: $permissions")
                    onGranted()
                }

                override fun onPermissionsDenied(
                    deniedPermissions: List<String>,
                    permanentlyDeniedPermissions: List<String>
                ) {
                    Timber.tag(TAG).d("Storage permission denied: $deniedPermissions")
                    onDenied()
                }
            }
        )
    }

    /**
     * 请求摄像头和麦克风权限
     */
    private fun requestCameraMicrophonePermission(
        activity: AppCompatActivity,
        onGranted: () -> Unit,
        onDenied: () -> Unit
    ) {
        val allPermissions = CAMERA_PERMISSIONS + MICROPHONE_PERMISSIONS
        PermissionUtils.requestPermissions(
            activity,
            allPermissions,
            REQUEST_CODE_CAMERA_MICROPHONE,
            object : PermissionCallback {
                override fun onPermissionsGranted(permissions: List<String>) {
                    Timber.tag(TAG).d("Camera and microphone permissions granted successfully: $permissions")
                    onGranted()
                }

                override fun onPermissionsDenied(
                    deniedPermissions: List<String>,
                    permanentlyDeniedPermissions: List<String>
                ) {
                    Timber.tag(TAG).d("Camera and microphone permissions denied: $deniedPermissions")
                    onDenied()
                }
            }
        )
    }


    /**
     * 处理权限请求结果
     */
    fun handleRequestPermissionsResult(
        activity: Activity,
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        PermissionUtils.handleRequestPermissionsResult(activity, requestCode, permissions, grantResults)
    }

    /**
     * 检查特定权限是否已授予
     */
    fun hasCameraPermission(context: Context): Boolean {
        return PermissionUtils.hasAllPermissions(context, CAMERA_PERMISSIONS)
    }

    fun hasMicrophonePermission(context: Context): Boolean {
        return PermissionUtils.hasAllPermissions(context, MICROPHONE_PERMISSIONS)
    }

    fun hasStoragePermission(context: Context): Boolean {
        return PermissionUtils.hasAllPermissions(context, STORAGE_PERMISSIONS)
    }

    fun hasCameraMicrophonePermission(context: Context): Boolean {
        val allPermissions = CAMERA_PERMISSIONS + MICROPHONE_PERMISSIONS
        return PermissionUtils.hasAllPermissions(context, allPermissions)
    }

    /**
     * 检查是否应该显示权限说明（用于判断权限状态）
     * @param activity Activity实例
     * @param permission 权限名称
     * @return true表示可以再次询问权限，false表示权限被永久拒绝
     */
    fun shouldShowRequestPermissionRationale(activity: AppCompatActivity, permission: String): Boolean {
        return PermissionUtils.shouldShowRequestPermissionRationale(activity, permission)
    }

    /**
     * 跳转到应用设置页面
     */
    fun openAppSettings(activity: AppCompatActivity) {
        Handler(Looper.getMainLooper()).postDelayed({
            PermissionUtils.openAppSettings(activity)
        },1000)

    }
}