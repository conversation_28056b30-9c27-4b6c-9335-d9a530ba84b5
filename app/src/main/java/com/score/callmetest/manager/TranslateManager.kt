package com.score.callmetest.manager

import com.google.gson.Gson
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.ThreadUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONArray
import org.json.JSONObject
import timber.log.Timber
import java.lang.ref.WeakReference
import java.security.MessageDigest
import java.util.LinkedHashMap
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

object TranslateManager {
    // region 缓存配置
    private const val CACHE_MAX_SIZE = 300
    private const val CACHE_FLUSH_THRESHOLD = 50
    private const val CACHE_FLUSH_INTERVAL_MS = 10_000L
    private const val PREF_NAME = "translate_cache"
    private const val PREF_KEY = "cache_json"
    private var lastFlushTime = 0L
    private val cacheLock = ReentrantLock()
    // endregion

    // region LRU缓存实现
    private val lruCache = object : LinkedHashMap<String, String>(CACHE_MAX_SIZE, 0.75f, true) {
        override fun removeEldestEntry(eldest: MutableMap.MutableEntry<String, String>?): Boolean {
            return size > CACHE_MAX_SIZE
        }
    }
    // endregion

    // region md5工具
    private fun String.md5(): String {
        val md = MessageDigest.getInstance("MD5")
        val bytes = md.digest(this.toByteArray())
        return bytes.joinToString("") { "%02x".format(it) }
    }
    // endregion

    // region 缓存落地
    private fun flushCacheIfNeeded(force: Boolean = false) {
        val now = System.currentTimeMillis()
        cacheLock.withLock {
            if (force || lruCache.size >= CACHE_FLUSH_THRESHOLD || now - lastFlushTime > CACHE_FLUSH_INTERVAL_MS) {
                val json = JSONArray()
                lruCache.forEach { (k, v) ->
                    val obj = JSONObject()
                    obj.put("k", k)
                    obj.put("v", v)
                    json.put(obj)
                }
                SharePreferenceUtil.putString(PREF_KEY, json.toString(), PREF_NAME)
                lastFlushTime = now
                Timber.Forest.tag("TranslateManager").d("缓存已落地, size=${lruCache.size}")
            }
        }
    }

    fun loadCacheAsync() {
        ThreadUtils.runOnIO {
            val jsonStr = SharePreferenceUtil.getString(PREF_KEY, null, PREF_NAME)
            if (!jsonStr.isNullOrEmpty()) {
                try {
                    val arr = JSONArray(jsonStr)
                    cacheLock.withLock {
                        lruCache.clear()
                        for (i in 0 until arr.length()) {
                            val obj = arr.getJSONObject(i)
                            lruCache[obj.getString("k")] = obj.getString("v")
                        }
                    }
                    Timber.Forest.tag("TranslateManager").d("缓存已加载, size=${lruCache.size}")
                } catch (e: Exception) {
                    Timber.Forest.tag("TranslateManager").e(e, "缓存加载失败")
                }
            }
        }
    }
    // endregion

    // 获取翻译接口地址
    private fun getTranslateUrl(): String? {
        // 假设 AppConfigManager 有获取 config item 的方法
        return AppConfigManager.getConfigValue("translate_v2")
    }

    /**
     * 翻译文本（回调方式）
     * @param text 需要翻译的文本
     * @param targetLang 目标语言（如 zh_Hant）
     * @param callback 翻译结果回调，参数为翻译后的文本，失败为 null
     */
    fun translate(
        scope: CoroutineScope = CoroutineScope(Dispatchers.IO),
        text: String,
        targetLang: String,
        callback: ((String?) -> Unit)? = null
    ) {
        val cacheKey = (text + "_" + targetLang).md5()
        val wrCallback = WeakReference<(String?) -> Unit>(callback)
        cacheLock.withLock {
            lruCache[cacheKey]?.let {
                wrCallback.get()?.invoke(it)
                return
            }
        }
        val url = getTranslateUrl() ?: run {
            wrCallback.get()?.invoke(null)
            return
        }
        val client = OkHttpClient()
        val requestObj = TranslateRequest(text, targetLang)
        val mediaType = "application/json".toMediaTypeOrNull()
        val body = Gson().toJson(requestObj).toRequestBody(mediaType)
        val request = Request.Builder()
            .url(url)
            .post(body)
            .build()

        scope.launch(Dispatchers.IO) {
            try {
                client.newCall(request).execute().use { response ->
                    if (!response.isSuccessful) {
                        wrCallback.get()?.invoke(null)
                        return@use
                    }
                    val bodyStr = response.body?.string() ?: run {
                        wrCallback.get()?.invoke(null)
                        return@use
                    }
                    val json = JSONObject(bodyStr)
                    val result = json.optJSONObject("data")
                        ?.optJSONArray("translations")
                        ?.optJSONObject(0)
                        ?.optString("translatedText")
                    if (result != null) {
                        cacheLock.withLock {
                            lruCache[cacheKey] = result
                        }
                        flushCacheIfNeeded()
                    }
                    wrCallback.get()?.invoke(result)
                }
            } catch (e: Exception) {
                Timber.Forest.tag("TranslateManager").e(e)
                wrCallback.get()?.invoke(null)
            }
        }
    }
}

data class TranslateRequest(
    val q: String,
    val target: String,
    val format: String = "text"
)