package com.score.callmetest.ui.main

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.graphics.toColorInt
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.databinding.ItemRechargeOptionBinding
import com.score.callmetest.manager.CountdownManager
import com.score.callmetest.util.AnimatorUtil
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.TimeUtils
import com.score.callmetest.util.click
import timber.log.Timber
import java.lang.ref.WeakReference

class RechargeOptionAdapter(
    private var options: List<RechargeOption>,
    private val onClick: (RechargeOption) -> Unit,
    private val limitToSix: Boolean = false, // 参数控制是否限制6个
    private val onCountdownEnd: (() -> Unit)? = null
) : RecyclerView.Adapter<RechargeOptionAdapter.ViewHolder>() {

    // 处理后的展示数据（排序+可选限制6个）
    private val displayOptions: List<RechargeOption> by lazy {
        val sortedOptions = options.sortedByDescending { option ->
            when (option.type) {
                "2" -> 2 // 活动商品最高优先级
                "1" -> 1 // 促销商品中等优先级
                else -> 0 // 普通商品最低优先级
            }
        }

        if (limitToSix) {
            sortedOptions.take(6) // 限制6个
        } else {
            sortedOptions // 不限制数量
        }
    }
    // 提供两种创建方式
    companion object {
        // 方式1：排序并限制6个
        fun createWithLimit(
            options: List<RechargeOption>,
            onClick: (RechargeOption) -> Unit,
            onCountdownEnd: (() -> Unit)?
        ): RechargeOptionAdapter {
            return RechargeOptionAdapter(options, onClick, true, onCountdownEnd)
        }

        // 方式2：只排序不限制数量
        fun createWithoutLimit(
            options: List<RechargeOption>,
            onClick: (RechargeOption) -> Unit,
            onCountdownEnd: (() -> Unit)?
        ): RechargeOptionAdapter {
            return RechargeOptionAdapter(options, onClick, false, onCountdownEnd)
        }
    }


    // 存储监听者ID，用于清理
    private val listenerIds = mutableMapOf<Int, String>()

    // 活跃的ViewHolder弱引用集合
    private val activeViewHolders = mutableSetOf<WeakReference<ViewHolder>>()

    // 旋转动画相关常量
    private val ROTATE_DURATION = 6000L

    // SVGA动画文件名常量
    private val COUNTDOWN_SVGA_NAME = "countdown.svga" // 倒计时背景动画文件名

    var selectedIndex: Int = 0
        set(value) {
            val old = field
            if (old != value) {
                field = value
                notifyItemChanged(old)
                notifyItemChanged(field)
            }
        }

    inner class ViewHolder(val binding: ItemRechargeOptionBinding) : RecyclerView.ViewHolder(binding.root) {
        var listenerId: String? = null
        // 添加旋转动画引用
        var rotateAnimator: ObjectAnimator? = null
        var scaleAnimatorSet: AnimatorSet? = null
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemRechargeOptionBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val option = displayOptions[position]

        // 根据商品类型设置不同的图标
        when (option.type) {
            "1", "2" -> {
                // 促销商品：设置促销图标
                holder.binding.ivIcon1.setImageResource(option.iconRes)
            }
            else -> {
                // 普通商品：设置普通图标
                holder.binding.ivIcon.setImageResource(option.iconRes)
            }
        }

        holder.binding.tvCoin.text = option.coinAmount.toString()

        // 设置价格信息
        setupPriceInfo(holder, option)

        if (option.extraCoinPercent != null && option.extraCoinPercent > 0) {
            holder.binding.tvBonus.text = "+${option.extraCoinPercent}%"
            holder.binding.imageCoin.visibility = View.VISIBLE
            holder.binding.tvBonus.visibility = View.VISIBLE
        } else {
            holder.binding.tvBonus.visibility = View.GONE
            holder.binding.imageCoin.visibility = View.GONE
        }

        // 设置HOT标签的可见性和内容
        setupHotLabel(holder, option, position)

        val selected = position == selectedIndex
        holder.itemView.isSelected = selected

        // 使用AnimatorUtil制作选中时的动效
        if (selected) {
            // 先清理之前的动画
            cancelIconAnimations(holder)

            // 根据商品类型选择不同的图标和背景动画
            when (option.type) {
                "1", "2" -> {
                    // 促销商品：使用促销专用的图标和背景动画
                    setupPromotionSelectedAnimation(holder)
                }
                else -> {
                    // 普通商品：使用普通的图标和背景动画
                    setupNormalSelectedAnimation(holder)
                }
            }
        } else {
            // 清理动画并恢复原始状态
            cancelIconAnimations(holder)

            // 根据商品类型恢复不同的图标状态
            when (option.type) {
                "1", "2" -> {
                    // 促销商品：恢复促销图标状态
                    restorePromotionIconState(holder)
                }
                else -> {
                    // 普通商品：恢复普通图标状态
                    restoreNormalIconState(holder)
                }
            }
        }

        // 设置价格布局背景
        holder.binding.priceLayout.background = DrawableUtils.createGradientDrawable(
            colors = intArrayOf("#FFF4CD".toColorInt(), "#FFFEDB".toColorInt()),
            orientation = GradientDrawable.Orientation.TOP_BOTTOM,
        )
        // 设置UI布局和背景
        setupUILayoutAndBackground(holder, option, selected)

        // 设置点击事件
        setupClickEvents(holder, option, position)

        // 记录活跃ViewHolder
        activeViewHolders.add(WeakReference(holder))
    }

    override fun getItemCount(): Int = displayOptions.size


    override fun onViewRecycled(holder: ViewHolder) {
        super.onViewRecycled(holder)

        // 清理倒计时监听者
        val position = holder.adapterPosition
        if (position != RecyclerView.NO_POSITION) {
            val listenerId = listenerIds[position]
            if (listenerId != null) {
                val option = options.getOrNull(position)
                if (option?.goodsCode != null) {
                    CountdownManager.removeListener(
                        CountdownManager.ActivityType.GOODS_PROMOTION,
                        option.goodsCode,
                        listenerId
                    )
                }
                listenerIds.remove(position)
            }
        }

        holder.listenerId = null

        // 清理SVGA动画
        holder.binding.svgaCountdown.stopAnimation()
        holder.binding.svgaCountdown.clear()

        // 隐藏倒计时容器
        holder.binding.frameCountdowm.visibility = View.GONE

        // 清理图标动画
        cancelIconAnimations(holder)

        // 恢复普通图标原始状态
        holder.binding.ivIcon.scaleX = 1f
        holder.binding.ivIcon.scaleY = 1f
        holder.binding.ivIcon.rotation = 0f

        // 恢复促销图标原始状态
        holder.binding.ivIcon1.scaleX = 1f
        holder.binding.ivIcon1.scaleY = 1f
        holder.binding.ivIcon1.rotation = 0f

        // 恢复普通背景动画容器原始状态
        holder.binding.bgAnimationImage.rotation = 0f
        holder.binding.bgAnimationImage.visibility = View.GONE

        // 恢复促销背景动画容器原始状态
        holder.binding.bgAnimationImage1.rotation = 0f
        holder.binding.bgAnimationImage1.visibility = View.GONE

        // 移除活跃ViewHolder引用
        activeViewHolders.removeAll { it.get() == holder || it.get() == null }
    }

    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        super.onDetachedFromRecyclerView(recyclerView)

        // 清理所有监听者
        listenerIds.forEach { (position, listenerId) ->
            val option = options.getOrNull(position)
            if (option?.goodsCode != null) {
                CountdownManager.removeListener(
                    CountdownManager.ActivityType.GOODS_PROMOTION,
                    option.goodsCode,
                    listenerId
                )
            }
        }
        listenerIds.clear()

        // 取消所有活跃ViewHolder上的动画，彻底释放引用
        activeViewHolders.forEach { ref ->
            ref.get()?.let { cancelIconAnimations(it) }
        }
        activeViewHolders.clear()

        // 清理所有监听者
        clearAllListeners()
    }

    private fun setupHotLabel(holder: ViewHolder, option: RechargeOption, position: Int) {
        // 清理之前的监听者
        val oldListenerId = listenerIds[position]
        if (oldListenerId != null && option.goodsCode != null) {
            CountdownManager.removeListener(
                CountdownManager.ActivityType.GOODS_PROMOTION,
                option.goodsCode,
                oldListenerId
            )
            listenerIds.remove(position)
        }
        holder.listenerId = null

        when (option.type) {
            "1", "2" -> {
                // 促销商品和活动商品：使用CountdownManager管理倒计时
                val goodsCode = option.goodsCode
                if (!goodsCode.isNullOrEmpty() && option.remainMilliseconds != null && option.remainMilliseconds > 0) {
                    // 初始化或更新倒计时状态（使用商品促销类型）
                    CountdownManager.initOrUpdateCountdown(
                        CountdownManager.ActivityType.GOODS_PROMOTION,
                        goodsCode,
                        option.remainMilliseconds
                    )

                    // 检查是否已过期
                    if (CountdownManager.isExpired(CountdownManager.ActivityType.GOODS_PROMOTION, goodsCode)) {
                        // 隐藏所有倒计时相关UI
                        holder.binding.tvHotRed.visibility = View.GONE
                        holder.binding.frameCountdowm.visibility = View.GONE
                        onCountdownEnd?.invoke()
                    } else {
                        // 显示SVGA倒计时动画，隐藏HOT标签
                        setupCountdownAnimation(holder, goodsCode, position)
                    }
                } else {
                    // 隐藏所有倒计时相关UI
                    holder.binding.tvHotRed.visibility = View.GONE
                    holder.binding.frameCountdowm.visibility = View.GONE
                }
            }
            else -> {
                // 其他类型：隐藏倒计时动画，显示原有的tags逻辑
                holder.binding.frameCountdowm.visibility = View.GONE
                if (!option.tags.isNullOrEmpty()) {
                    holder.binding.tvHotRed.visibility = View.VISIBLE
                    holder.binding.tvHotRed.text = option.tags
                } else {
                    holder.binding.tvHotRed.visibility = View.GONE
                }
            }
        }
    }

    private fun setupCountdownAnimation(holder: ViewHolder, goodsCode: String, position: Int) {
        // 显示倒计时容器，隐藏HOT标签
        holder.binding.frameCountdowm.visibility = View.VISIBLE
        holder.binding.tvHotRed.visibility = View.GONE

        // 播放SVGA倒计时背景动画（循环播放）
        CustomUtils.playSvga(
            holder.binding.svgaCountdown,
            COUNTDOWN_SVGA_NAME, // 使用常量定义的SVGA文件名
            loops = 0 // 无限循环
        )

        // 启动倒计时
        startCountdownWithSvga(holder, goodsCode, position)
    }

    private fun startCountdownWithSvga(holder: ViewHolder, goodsCode: String, position: Int) {
        val listenerId = CountdownManager.startCountdown(
            activityType = CountdownManager.ActivityType.GOODS_PROMOTION,
            activityId = goodsCode,
            onTick = { remainingMillis ->
                ThreadUtils.runOnMain {
                    // 更新倒计时文本显示在SVGA动画上方
                    val timeText = TimeUtils.formatSecondsToTime(remainingMillis / 1000)
                    holder.binding.tvCountdown.text = timeText
                }
            },
            onFinish = {
                ThreadUtils.runOnMain {
                    // 倒计时结束，隐藏整个倒计时容器
                    holder.binding.frameCountdowm.visibility = View.GONE
                    holder.listenerId = null
                    listenerIds.remove(position)

                    // 通知外部计时结束
                    onCountdownEnd?.invoke()
                }
            }
        )

        holder.listenerId = listenerId
        if (listenerId != null) {
            listenerIds[position] = listenerId
        }
    }

    /**
     * 取消图标动画(金币闪光)
     */
    private fun cancelIconAnimations(holder: ViewHolder) {
        // 统一安全释放动画，彻底断开与 View 的引用，防止内存泄漏
        AnimatorUtil.releaseAnimator(holder.rotateAnimator)
        holder.rotateAnimator = null

        AnimatorUtil.releaseAnimator(holder.scaleAnimatorSet)
        holder.scaleAnimatorSet = null

        // 重置普通图标状态
        holder.binding.ivIcon.run {
            animate().cancel()
            clearAnimation()
        }

        // 重置促销图标状态
        holder.binding.ivIcon1.run {
            animate().cancel()
            clearAnimation()
        }

        // 重置普通背景动画容器状态
        holder.binding.bgAnimationImage.run {
            animate().cancel()
            clearAnimation()
            rotation = 0f
            visibility = View.GONE
        }

        // 重置促销背景动画容器状态
        holder.binding.bgAnimationImage1.run {
            animate().cancel()
            clearAnimation()
            rotation = 0f
            visibility = View.GONE
        }
    }

    /**
     * 设置价格信息（根据商品类型优化判断逻辑）
     */
    private fun setupPriceInfo(holder: ViewHolder, option: RechargeOption) {
        // 设置当前价格
        holder.binding.tvPrice.text = option.price

        // 设置旧价格信息
        setupOldPrice(
            priceTextView = holder.binding.tvOldPrice,
            option = option
        )
    }

    /**
     * 设置旧价格的显示和下划线
     */
    private fun setupOldPrice(priceTextView: TextView, option: RechargeOption) {
        // 检查旧价格是否有效：不为null、不为空字符串，且大于当前价格
        if (!option.oldPrice.isNullOrEmpty() &&
            option.oldPrice.isNotBlank() &&
            CustomUtils.comparePrice(option.oldPrice, option.price) > 0) {
            priceTextView.text = option.oldPrice
            priceTextView.paint.isStrikeThruText = true
            priceTextView.visibility = View.VISIBLE
        } else {
            priceTextView.text = ""
            priceTextView.paint.isStrikeThruText = false
            priceTextView.visibility = View.GONE
        }
    }

    /**
     * 根据商品类型设置UI布局和背景
     */
    private fun setupUILayoutAndBackground(holder: ViewHolder, option: RechargeOption, selected: Boolean) {
        when (option.type) {
            "1", "2" -> setupPromotionProductUI(holder)
            else -> setupNormalProductUI(holder, selected)
        }
    }

    /**
     * 设置促销商品和活动商品的UI
     */
    private fun setupPromotionProductUI(holder: ViewHolder) {
        // 显示促销价格布局
        holder.binding.priceLayout.visibility = View.VISIBLE

        // 促销商品：显示促销专用的图标，背景动画只在选中时显示
        holder.binding.ivIcon1.visibility = View.VISIBLE

        // 隐藏普通商品的图标和背景动画
        holder.binding.bgAnimationImage.visibility = View.GONE
        holder.binding.ivIcon.visibility = View.GONE

        // 设置卡片紫色背景
        holder.binding.cardView.background = DrawableUtils.createRoundRectDrawable(
            color = "#C19BFF".toColorInt(),
            radius = DisplayUtils.dp2pxInternalFloat(18f)
        )
    }

    /**
     * 设置普通商品的UI
     */
    private fun setupNormalProductUI(holder: ViewHolder, selected: Boolean) {
        // 显示价格布局
        holder.binding.priceLayout.visibility = View.VISIBLE

        // 普通商品：显示普通的图标，背景动画只在选中时显示
        holder.binding.ivIcon.visibility = View.VISIBLE

        // 隐藏促销专用的图标和背景动画
        holder.binding.bgAnimationImage1.visibility = View.GONE
        holder.binding.ivIcon1.visibility = View.GONE

        // 设置卡片白色背景（普通商品始终保持白色）
        holder.binding.cardView.background = DrawableUtils.createRoundRectDrawable(
            color = Color.WHITE,
            radius = DisplayUtils.dp2pxInternalFloat(18f)
        )
    }

    /**
     * 设置点击事件
     */
    private fun setupClickEvents(holder: ViewHolder, option: RechargeOption, position: Int) {
        // 确保 price_layout 不会拦截点击事件
        holder.binding.priceLayout.isClickable = false
        holder.binding.priceLayout.isFocusable = false

        // 设置整个item的点击事件（选中逻辑 + 支付逻辑）
        holder.itemView.click {
            if (selectedIndex != position) {
                selectedIndex = position
            }
            // 直接跳转支付界面
            onClick(option)
        }
    }

    /**
     * 设置促销商品选中时的动画
     */
    private fun setupPromotionSelectedAnimation(holder: ViewHolder) {
        // 显示促销背景动画容器
        holder.binding.bgAnimationImage1.visibility = View.VISIBLE

        // 对促销图标添加缩放动画效果
        holder.scaleAnimatorSet = AnimatorUtil.scale(
            view = holder.binding.ivIcon1,
            scaleFrom = 1f,
            scaleTo = 1.1f,
            scaleDuration = 300
        ) {
            // 缩放完成后开始背景容器的旋转动画
            holder.rotateAnimator = AnimatorUtil.rotate(
                view = holder.binding.bgAnimationImage1,
                duration = ROTATE_DURATION,
                repeatCount = ObjectAnimator.INFINITE
            )
        }
    }

    /**
     * 设置普通商品选中时的动画
     */
    private fun setupNormalSelectedAnimation(holder: ViewHolder) {
        // 显示普通背景动画容器
        holder.binding.bgAnimationImage.visibility = View.VISIBLE

        // 对普通图标添加缩放动画效果
        holder.scaleAnimatorSet = AnimatorUtil.scale(
            view = holder.binding.ivIcon,
            scaleFrom = 1f,
            scaleTo = 1.1f,
            scaleDuration = 300
        ) {
            // 缩放完成后开始背景容器的旋转动画
            holder.rotateAnimator = AnimatorUtil.rotate(
                view = holder.binding.bgAnimationImage,
                duration = ROTATE_DURATION,
                repeatCount = ObjectAnimator.INFINITE
            )
        }
    }

    /**
     * 恢复促销商品图标状态
     */
    private fun restorePromotionIconState(holder: ViewHolder) {
        // 隐藏促销背景动画容器
        holder.binding.bgAnimationImage1.visibility = View.GONE

        // 恢复促销图标原始大小
        holder.scaleAnimatorSet = AnimatorUtil.scale(
            view = holder.binding.ivIcon1,
            scaleFrom = holder.binding.ivIcon1.scaleX,
            scaleTo = 1f,
            scaleDuration = 200
        )
    }

    /**
     * 恢复普通商品图标状态
     */
    private fun restoreNormalIconState(holder: ViewHolder) {
        // 隐藏普通背景动画容器
        holder.binding.bgAnimationImage.visibility = View.GONE

        // 恢复普通图标原始大小
        holder.scaleAnimatorSet = AnimatorUtil.scale(
            view = holder.binding.ivIcon,
            scaleFrom = holder.binding.ivIcon.scaleX,
            scaleTo = 1f,
            scaleDuration = 200
        )
    }

    /**
     * 清理所有倒计时监听者，防止内存泄露
     * 应该在 Activity onDestroy 时调用
     */
    fun clearAllListeners() {
        listenerIds.forEach { (position, listenerId) ->
            val option = options.getOrNull(position)
            if (option?.goodsCode != null) {
                CountdownManager.removeListener(
                    CountdownManager.ActivityType.GOODS_PROMOTION,
                    option.goodsCode,
                    listenerId
                )
            }
        }
        listenerIds.clear()
        Timber.tag("RechargeOptionAdapter").d("清理所有倒计时监听者")
    }
}