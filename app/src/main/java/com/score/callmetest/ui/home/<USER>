package com.score.callmetest.ui.home

import android.os.Handler
import android.os.Looper
import androidx.lifecycle.viewModelScope
import com.score.callmetest.CallStatus
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.manager.UserInfoManager.getUserInfo
import com.score.callmetest.network.BroadcasterModel
import com.score.callmetest.network.GetUserListOnlineStatusPostV2Request
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.network.SearchBroadcastersRequest
import com.score.callmetest.ui.base.BaseViewModel
import com.score.callmetest.util.EventBus
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Runnable
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList

class WallViewModel(
    private val tab1Name: String,
    private val tab2Name: String,
    private var regionCode: String? = null
) : BaseViewModel() {
    private val coroutineScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    private val handler = Handler(Looper.getMainLooper())
    private val SCROLL_STOP_DELAY = 500L // 0.5秒
    private val PAGE_SIZE = 20

    // 数据与状态
    private var currentPage = 1
    private var isLoading = false
    private var hasMore = true
    private val broadcasterList = CopyOnWriteArrayList<BroadcasterModel>()

    // 回调接口
    var onDataChanged: ((List<BroadcasterModel>, Boolean) -> Unit)? = null
    var onError: ((String) -> Unit)? = null

    /**
     * 加载第一页（下拉刷新）
     */
    fun refresh() {
        currentPage = 1
        hasMore = true
        loadPage(isRefresh = true)
    }

    /**
     * 加载更多
     */
    fun loadMore() {
        if (!hasMore || isLoading) return
        currentPage++
        loadPage(isRefresh = false)
    }

    /**
     * 加载数据
     */
    private fun loadPage(isRefresh: Boolean) {
        if (isLoading) return
        isLoading = true
        coroutineScope.launch {
            try {
                val request = SearchBroadcastersRequest(
                    limit = PAGE_SIZE,
                    page = currentPage,
                    tag = tab2Name,
                    isPageMode = true,
                    isRemoteImageUrl = true,
                    category = tab1Name,
                    region = if (regionCode == "ALL") null else regionCode
                )
                val response = RetrofitUtils.dataRepository.searchBroadcasters(request)
                if (response is NetworkResult.Success) {
                    val newList = response.data ?: emptyList()
                    hasMore = newList.size > 0
                    if (isRefresh) {
                        broadcasterList.clear()
                    }

                    // 适配审核模式
                    if (StrategyManager.isReviewPkg()) {
                        newList.forEach { reviewBcaster ->
                            if (reviewBcaster.isAnswer == true && !StrategyManager.reviewPkgUsers.contains(
                                    reviewBcaster.userId
                                )
                            ) {
                                reviewBcaster.status = CallStatus.ONLINE
                            } else {
                                reviewBcaster.status =
                                    GlobalManager.getReviewOtherStatus(reviewBcaster.userId)
                            }
                        }
                    }

                    broadcasterList.addAll(newList)

                    // 发列表数据到匹配
                    EventBus.post(FlashBroadcasterIconsEvent(broadcasterList))

                    // 初始化状态缓存
                    newList.forEach { b ->
                        UserInfoManager.putInitialStatus(b.userId, b.status)
                    }
                    onDataChanged?.invoke(broadcasterList.toList(), hasMore)
                } else {
                    onError?.invoke("加载主播失败")
                }
            } catch (e: Exception) {
                onError?.invoke(e.message ?: "加载主播异常")
            } finally {
                isLoading = false
            }
        }
    }

    /**
     * 移除被拉黑的用户并更新UI显示
     */
    fun removeBlockedUser(userId: String) {
        // 从列表中移除被拉黑的用户
        val removed = broadcasterList.removeAll { it.userId == userId }
        if (removed) {
            // 更新UI
            onDataChanged?.invoke(broadcasterList.toList(), hasMore)
        }
    }


    /**
     * 滚动停止后500ms刷新
     */
    fun onScrollStateChanged(isScrolling: Boolean, refreshCallback: () -> Unit) {
        handler.removeCallbacksAndMessages(null)
        if (!isScrolling) {
            handler.postDelayed({ refreshCallback.invoke() }, SCROLL_STOP_DELAY)
        }
    }

    /**
     * 获取当前主播列表
     */
    fun getCurrentList(): List<BroadcasterModel> = broadcasterList.toList()
    fun hasMoreData(): Boolean = hasMore
    fun isLoading(): Boolean = isLoading

    /**
     * 更新国家筛选参数
     */
    fun updateRegion(region: String?) {
        if (regionCode != region) {
            regionCode = region
            // 重新加载数据
            refresh()
        }
    }

    /**
     * 清理资源
     */
    fun clear() {
        coroutineScope.cancel()
        broadcasterList.clear()
    }

    data class FlashBroadcasterIconsEvent(val broadcasterList: List<BroadcasterModel>)
} 