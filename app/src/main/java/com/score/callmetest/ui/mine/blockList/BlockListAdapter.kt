package com.score.callmetest.ui.mine.blockList

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.score.callmetest.CallmeApplication.Companion.context
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemFragmentBlocklistBinding
import com.score.callmetest.databinding.ItemListBottomBinding
import com.score.callmetest.network.BlockListItem
import com.score.callmetest.ui.mine.follow.BottomState
import com.score.callmetest.util.CountryUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.click

class BlockListAdapter(
    private val onUnblockClick: ((BlockListItem) -> Unit)? = null
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    companion object {
        private const val VIEW_TYPE_BLOCK = 0
        private const val VIEW_TYPE_BOTTOM = 1
    }
    private val data = mutableListOf<BlockListItem>()
    private var bottomState: BottomState = BottomState.HIDDEN

    override fun getItemCount(): Int = data.size + if (bottomState != BottomState.HIDDEN) 1 else 0

    override fun getItemViewType(position: Int): Int {
        return if (bottomState != BottomState.HIDDEN && position == data.size) VIEW_TYPE_BOTTOM else VIEW_TYPE_BLOCK
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_BLOCK -> BlockListViewHolder(ItemFragmentBlocklistBinding.inflate(LayoutInflater.from(parent.context), parent, false))
            VIEW_TYPE_BOTTOM -> BottomViewHolder(ItemListBottomBinding.inflate(LayoutInflater.from(parent.context), parent, false))
            else -> throw IllegalArgumentException("Unknown view type: $viewType")// todo 异常类型主动报错，后面可以删除
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is BlockListViewHolder && position < data.size) {
            val item = data[position]
            holder.binding.apply {
                // 昵称
                tvUsername.text = item.nickName ?: "--"
                // 头像
                val avatarUrl = item.avatar ?: ""
                if (avatarUrl.isNotEmpty()) {
                    Glide.with(ivAvatar.context)
                        .load(avatarUrl)
                        .placeholder(R.drawable.placeholder)
                        .error(R.drawable.placeholder)
                        .into(ivAvatar)
                } else {
                    ivAvatar.setImageResource(R.drawable.placeholder)
                }
                // 国家
                tvRegion.text = item.registerCountry ?: "ALL"
                ivFlag.setImageResource(CountryUtils.getIconByEnName(item.registerCountry))

                // 设置背景
                DrawableUtils.setRoundRectBackground(
                    tvUnBlock,
                    ContextCompat.getColor(context,R.color.block_Unblock), 
                    com.score.callmetest.util.DisplayUtils.dp2pxInternal(14f).toFloat()
                )

                // 解除 block 按钮
                tvUnBlock.click {
                    onUnblockClick?.invoke(item)
                }
            }
        } else if (holder is BottomViewHolder) {
            holder.bind("Bottom")
        }
    }

    fun setData(newData: List<BlockListItem>) {
        data.clear()
        data.addAll(newData)
        notifyDataSetChanged()
    }

    fun setBottomState(state: BottomState) {
        if (this.bottomState != state) {
            this.bottomState = state
            notifyDataSetChanged()
        }
    }

    class BlockListViewHolder(val binding: ItemFragmentBlocklistBinding) : RecyclerView.ViewHolder(binding.root)

    inner class BottomViewHolder(private val binding: ItemListBottomBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(text: String) {
            when (bottomState) {
                BottomState.LOADING -> {
                    // 显示加载动画，隐藏静态底部
                    binding.layoutLoading.visibility = android.view.View.VISIBLE
                    binding.layoutFinished.visibility = android.view.View.GONE
                }
                BottomState.FINISHED -> {
                    // 隐藏加载动画，显示静态底部
                    binding.layoutLoading.visibility = android.view.View.GONE
                    binding.layoutFinished.visibility = android.view.View.VISIBLE
                    binding.tvBottomText.text = text
                }
                BottomState.HIDDEN -> {
                    // 隐藏所有（这种情况下不应该调用bind，但为了安全起见）
                    binding.layoutLoading.visibility = android.view.View.GONE
                    binding.layoutFinished.visibility = android.view.View.GONE
                }
            }
        }
    }
}