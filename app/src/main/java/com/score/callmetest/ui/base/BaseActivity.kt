package com.score.callmetest.ui.base

import android.graphics.Color
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.viewbinding.ViewBinding
import com.score.callmetest.manager.AppLifecycleManager
import com.score.callmetest.manager.AppPermissionManager
import com.score.callmetest.manager.DualChannelEventManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.network.JoinChannelRequest
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.network.UpdateAgoraUidRequest
import com.score.callmetest.ui.message.MessageIncomingManager
import com.score.callmetest.ui.splash.SplashActivity
import com.score.callmetest.ui.videocall.CallIncomingManager
import com.score.callmetest.ui.videocall.VideoCallActivity
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.AgodaUtils
import com.score.callmetest.util.ClickUtils
import com.score.callmetest.util.StatusBarUtils
import com.score.callmetest.util.ThreadUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber

/**
## 调用顺序

```
onCreate()
├── getViewBinding()     // 获取ViewBinding
├── setContentView()     // 设置内容视图
├── createViewModel()    // 创建ViewModel
├── initView()          // 初始化视图
├── initData()          // 初始化数据
├── observeViewModel()   // 观察ViewModel
└── initListener()      // 初始化监听器
```

## 注意事项

1. 子类必须实现 `getViewBinding()` 抽象方法
2. 如果使用ViewModel，子类必须实现 `createViewModel()` 抽象方法
3. 在 `initView()`、`initData()`、`initListener()` 中可以直接使用 `binding` 属性
4. 在 `observeViewModel()` 中可以直接使用 `viewModel` 属性
5. 如果需要自定义 `onCreate()` 逻辑，请确保调用 `super.onCreate(savedInstanceState)`
6. ViewBinding和ViewModel的创建和销毁由基类自动管理
 **/
abstract class BaseActivity<VB : ViewBinding, VM : ViewModel> : AppCompatActivity() {
    protected lateinit var binding: VB
    protected lateinit var viewModel: VM

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        Timber.tag(javaClass.simpleName).d("onSaveInstanceState")
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        Timber.tag(javaClass.simpleName).d("onRestoreInstanceState")
        // 不进行恢复状态了，因为声网、融云等都已经断开，需要从头开始
        ActivityUtils.startActivityClearTask(this, SplashActivity::class.java)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        // 设置状态栏透明，图标默认为浅色
//        StatusBarUtils.setStatusBarTransparent(this, true)
        StatusBarUtils.showStatusBar(this, Color.TRANSPARENT, true)

        super.onCreate(savedInstanceState)

        // 调用抽象方法获取ViewBinding
        binding = getViewBinding()
        setContentView(binding.root)
        binding.root.overScrollMode = android.view.View.OVER_SCROLL_NEVER

        // 适配底部安全区域，防止custom_bottom_tab被系统导航栏遮挡
        ViewCompat.setOnApplyWindowInsetsListener(binding.root) { v, insets ->
            val bottom = insets.getInsets(WindowInsetsCompat.Type.systemBars()).bottom
            v.setPadding(v.paddingLeft, v.paddingTop, v.paddingRight, bottom)
            insets
        }

        // 创建ViewModel
        viewModel = ViewModelProvider(this)[getViewModelClass()]

        // 调用初始化方法
        initView()
        // 初始化监听器
        initListener()

        initData()

        // 清除所有点击记录，避免从其他Activity返回时的异常
        ClickUtils.clearAllClickRecords()
        Timber.tag("dsc--").d("${javaClass.simpleName} onCreate completed")

        doBaseLogic()
    }

    fun doBaseLogic() {
        // 已经非通话页，处理onCall消息（使用双通道去重）
        if (this !is VideoCallActivity) {
            DualChannelEventManager.observeOnCall(this) { onCallMessage ->
                try {
                    val activity = ActivityUtils.getTopActivity()
                    CallIncomingManager.handleIncomingCall(activity!!, onCallMessage)
                } catch (e: Exception) {
                    Timber.tag("${javaClass.simpleName}")
                        .e(e, "处理来电消息失败")
                }
            }
        } else {
        }

        // 初始化消息弹窗管理器（只需要初始化一次）
        MessageIncomingManager.initialize()

    }

    /**
     * 获取ViewBinding对象
     * 子类必须实现此方法来返回对应的ViewBinding
     */
    protected abstract fun getViewBinding(): VB

    /**
     * 创建ViewModel对象
     * 子类必须实现此方法来创建对应的ViewModel
     */
    protected abstract fun getViewModelClass(): Class<VM>

    /**
     * 初始化视图
     * 子类可以重写此方法来初始化视图
     */
    protected open fun initView() {}

    /**
     * 初始化数据
     * 子类可以重写此方法来初始化数据
     */
    protected open fun initData() {}

    /**
     * 初始化监听器
     * 子类可以重写此方法来初始化监听器
     */
    protected open fun initListener() {}

    /**
     * 处理权限请求结果
     * 子类可以重写此方法来处理权限请求结果
     */
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        AppPermissionManager.handleRequestPermissionsResult(
            this,
            requestCode,
            permissions,
            grantResults
        )
    }

    /**
     * 检查权限状态变化
     * 当Activity恢复时自动检查权限状态
     */
    override fun onResume() {
        super.onResume()
//        CallIncomingManager.shouldShowDialogInActivity(this)

        // 检查是否需要显示消息弹窗
        MessageIncomingManager.shouldShowDialogInActivity(this)

        // 清除所有点击记录，避免从其他Activity返回时的异常
        ClickUtils.clearAllClickRecords()
        Timber.tag("dsc--").d("${javaClass.simpleName} onResume called")
        ThreadUtils.runOnIODelayed(1000) {
            AppLifecycleManager.userModeSwitch()
        }
    }

    override fun onPause() {
        super.onPause()
        ThreadUtils.runOnIODelayed(1000) {
            AppLifecycleManager.userModeSwitch()
        }
    }
}
