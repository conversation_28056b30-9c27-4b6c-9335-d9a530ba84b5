package com.score.callmetest.ui.videocall

import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import com.score.callmetest.CallStatus
import com.score.callmetest.CallType
import com.score.callmetest.CallmeApplication
import com.score.callmetest.db.DatabaseFactory
import com.score.callmetest.entity.CallHistoryEntity
import com.score.callmetest.entity.ChatMessageEntity
import com.score.callmetest.entity.MessageEvents
import com.score.callmetest.entity.MessageListEntity
import com.score.callmetest.entity.MessageStatus
import com.score.callmetest.entity.MessageType
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.UserInfo
import com.score.callmetest.ui.base.BaseViewModel
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.TimeUtils
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.UUID


class VideoCallViewModel : BaseViewModel() {
    var userId: String? = null

    // 通话相关信息
    var avatarUrl: String? = null
    var nickname: String? = null
    var callStartTime: Long = 0L
    var callEndTime: Long = 0L
    var hasEnteredOngoing: Boolean = false // 是否进入了通话中状态

    /**
     * 保存通话记录到数据库
     * @param callType 通话类型，如 CallType.REJECTED_CALL
     * @param callDuration 通话时长（秒），默认为0
     * @param onlineStatus 通话结束后的用户在线状态
     */
    fun saveCallHistory(callType: String, callDuration: Long = 0L,onlineStatus: String = CallStatus.OFFLINE) {
        if (userId.isNullOrEmpty()) {
            Timber.tag("VideoCallViewModel").w("saveCallHistory: userId is empty, skip saving")
            return
        }

        viewModelScope.launch {
            try {
                // 通过UserInfoManager获取用户信息
                UserInfoManager.getUserInfo(userId!!,scope = viewModelScope) { userInfo ->
                    if (userInfo != null) {
                        // 使用获取到的用户信息构建通话记录
                        val callHistory = CallHistoryEntity(
                            id = UUID.randomUUID().toString(),
                            userId = userId!!,
                            currentUserId =  UserInfoManager.myUserInfo?.userId ?: "",
                            userName = userInfo.nickname ?: nickname ?: "",
                            avatar = userInfo.avatarUrl ?: userInfo.avatarThumbUrl ?: userInfo.avatarMiddleThumbUrl ?: avatarUrl ?: "",
                            unitPrice = userInfo.unitPrice ?: -1,   //如果获取不到单位价格，则设置为-1
                            callType = callType,
                            callDuration = callDuration,
                            callEndTime = System.currentTimeMillis(),
                            hasVideo = true, // 视频通话
                            onlineStatus = onlineStatus, // 默认离线状态
                            isPinned = false,
                            isBottomView = false
                        )

                        DatabaseFactory.getDatabase(CallmeApplication.context)
                            .insertCallHistory(callHistory) { success ->
                                if (success) {
                                    Timber.tag("VideoCallViewModel").d("Call history saved successfully: ${callHistory.callType} for user ${callHistory.userId}, avatar=${callHistory.avatar}")
                                } else {
                                    Timber.tag("VideoCallViewModel").e("Failed to save call history: ${callHistory.callType} for user ${callHistory.userId}")
                                }
                            }
                        
                        // 添加通话记录到chat数据库
                        saveToChatMessage(callHistory,userInfo)
                        
                    } else {
                        Timber.tag("VideoCallViewModel").w("saveCallHistory: failed to get user info for userId: $userId")
                    }
                }
            } catch (e: Exception) {
                Timber.tag("VideoCallViewModel").e(e, "Error saving call history")
            }
        }
    }
    
    /**
     * 保存通话记录到聊天消息数据库
     */
    private fun saveToChatMessage(callHistory: CallHistoryEntity,userInfo: UserInfo) {
        try {
            val isCurrentUser = when(callHistory.callType){
                CallType.OUTGOING_CALL -> true    // 拨出且接通（内部使用，显示时会转换为INCOMING_CALL）
                CallType.CANCELLED_CALL -> true   // 拨出并自己取消的通话
                CallType.UNANSWERED_CALL -> true  // 拨出自己没有取消但对方挂断、后台异常挂断或超时挂断
                CallType.INCOMING_CALL -> false    // 收到且接通，以及拨出且接通（显示用）
                CallType.REJECTED_CALL -> false    // 收到且用户挂断
                CallType.MISSED_CALL -> false       // 收到且未点接通
                else -> return
            }

            val chatMessage = ChatMessageEntity(
                messageId = UUID.randomUUID().toString(),
                currentUserId = callHistory.currentUserId,
                senderId = if(isCurrentUser) callHistory.currentUserId else callHistory.userId,
                senderName = if(isCurrentUser) UserInfoManager.myUserInfo?.nickname ?: "" else callHistory.userName,
                senderAvatar = if(isCurrentUser) UserInfoManager.myUserInfo?.avatarUrl ?: "" else callHistory.avatar,
                receiverId = if(isCurrentUser) callHistory.userId else callHistory.currentUserId,
                content = "",
                messageType = MessageType.CALL,
                status = if(isCurrentUser) MessageStatus.SENT else MessageStatus.RECEIVED,
                contentType = callHistory.callType,
                mediaDuration = callHistory.callDuration,
                timestamp = callHistory.callEndTime,
                isCurrentUser = isCurrentUser
            )

            DatabaseFactory.getDatabase(CallmeApplication.context)
                .insertChatMessage(chatMessage) { success ->
                    if (success) {
                        EventBus.post(MessageEvents.NewCallMessage(chatMessage))
                        Timber.tag("VideoCallViewModel").d("Chat message saved successfully for call with user ${callHistory.userId}")
                    } else {
                        Timber.tag("VideoCallViewModel").e("Failed to save chat message for call with user ${callHistory.userId}")
                    }
                }

            // 添加通话记录到chatList数据库
            saveToMsgList(callHistory,userInfo)
        } catch (e: Exception) {
            Timber.e(e)
        }
    }

    /**
     * 保存通话记录到消息列表数据库
     */
    private fun saveToMsgList(callHistory: CallHistoryEntity,userInfo: UserInfo) {
        try {
            val isOfficialUser = StrategyManager.isTopOfficialUser(userInfo.userId?:"")

            // 先查数据库有没有此人-有则更新-无则新增
            DatabaseFactory.getDatabase(CallmeApplication.context)
                .getMessageListById(userInfo.userId?:""){ entity ->
                    if(entity == null){
                        // 数据库没有--新增
                        val newEntity = MessageListEntity(
                            userId = userInfo.userId?:"",
                            currentUserId = callHistory.currentUserId,
                            userName = userInfo.nickname?:"",
                            gender = userInfo.gender?:1,
                            unitPrice = userInfo.unitPrice?:0,
                            avatar = userInfo.avatar?:"",
                            avatarThumbUrl = userInfo.avatarThumbUrl?:"",
                            lastMessage = "",
                            lastMessageType = MessageType.CALL,
                            timestamp = TimeUtils.formatTimestampForChatList(callHistory.callEndTime),
                            timeInMillis = callHistory.callEndTime,
                            unreadCount = 1,
                            onlineStatus = if(isOfficialUser) CallStatus.UNKNOWN else callHistory.onlineStatus,
                            isPinned = isOfficialUser
                        )
                        DatabaseFactory.getDatabase(CallmeApplication.context)
                            .insertMessageList(newEntity)

                        return@getMessageListById
                    }
                    // 数据库有--更新
                    entity.apply {
                        lastMessage= ""
                        lastMessageType = MessageType.CALL
                        timestamp = TimeUtils.formatTimestampForChatList(callHistory.callEndTime)
                        timeInMillis = callHistory.callEndTime
                        unreadCount = entity.unreadCount + 1
                        onlineStatus = if(isOfficialUser) CallStatus.UNKNOWN else callHistory.onlineStatus
                        // 只有官方号手动强制置顶
                        if(isOfficialUser) {
                            isPinned = true
                        }
                    }
                    // 更新数据库
                    DatabaseFactory.getDatabase(CallmeApplication.context)
                        .updateCurrentUserMessageList(entity)
                }
        } catch (e: Exception) {
            Timber.e(e)
        }
    }

    /**
     * 标记通话开始时间
     */
    fun markCallStart() {
        callStartTime = System.currentTimeMillis()
        Timber.d("Call started at: $callStartTime")
    }

    /**
     * 标记通话结束时间并计算通话时长
     * @return 通话时长（秒）
     */
    fun markCallEnd(): Long {
        callEndTime = System.currentTimeMillis()
        val duration = if (callStartTime > 0) {
            (callEndTime - callStartTime) / 1000
        } else {
            0L
        }
        Timber.d("Call ended at: $callEndTime, duration: ${duration}s")
        return duration
    }

    /**
     * 设置用户信息
     */
    fun setUserInfo(userId: String, nickname: String?, avatarUrl: String?) {
        this.userId = userId
        this.nickname = nickname
        this.avatarUrl = avatarUrl
        Timber.d("User info set: userId=$userId, nickname=$nickname. avatarUrl=$avatarUrl")

        UserInfoManager.loadOnlineStatus(
            scope = viewModelScope,
            userId = this.userId!!,
            callback = { msg, throwable ->

            }
        )
    }

}