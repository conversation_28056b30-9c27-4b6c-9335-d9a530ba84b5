package com.score.callmetest.ui.widget

import android.content.DialogInterface
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.graphics.drawable.toDrawable
import androidx.core.graphics.toColorInt
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import com.opensource.svgaplayer.SVGAParser
import com.score.callmetest.R
import com.score.callmetest.manager.CountdownManager
import com.score.callmetest.manager.GoodsManager
import com.score.callmetest.network.GoodsInfo
import com.score.callmetest.ui.main.RechargeOption
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.TimeUtils
import com.score.callmetest.util.click
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import timber.log.Timber

class PromotionDialogFragment : DialogFragment() {

    private var amount: String = ""
    private var addAmount: String = ""
    private var description: String = ""
    private var price: String = ""
    private var oldPrice: String = ""
    private var buttonSvgaName: String = "btn_promotion.svga"
    private var buttonEffectSvgaName: String = "discount_tags.svga"
    private var remainingCount: Int = 0
    private var onButtonClickListener: (() -> Unit)? = null
    private var layoutResId: Int = R.layout.dialog_promotion
    private var treasureBoxImageUrl: String? = null

    // CountdownManager相关属性
    private var activityType: CountdownManager.ActivityType? = null
    private var activityId: String? = null

    // 倒计时相关
    private var countdownListenerId: String? = null

    companion object {
        fun newInstance(
            layoutResId: Int = R.layout.dialog_promotion,
            amount: String,
            description: String,
            price: String = "",
            originPrice: String = "",
            buttonSvgaName: String = "btn_promotion.svga",
            buttonEffectSvgaName: String = "discount_tags.svga",
            onButtonClickListener: (() -> Unit)? = null,
            addAmount: String = "",
            remainingCount: Int = 0,
            treasureBoxImageUrl: String? = null,
            activityType: CountdownManager.ActivityType,
            activityId: String
        ): PromotionDialogFragment {
            return PromotionDialogFragment().apply {
                this.layoutResId = layoutResId
                this.amount = amount
                this.addAmount = addAmount
                this.description = description
                this.price = price
                this.oldPrice = originPrice
                this.buttonSvgaName = buttonSvgaName
                this.buttonEffectSvgaName = buttonEffectSvgaName
                this.onButtonClickListener = onButtonClickListener
                this.remainingCount = remainingCount
                this.treasureBoxImageUrl = treasureBoxImageUrl
                this.activityType = activityType
                this.activityId = activityId
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 禁止点击外部和返回键关闭
        isCancelable = false
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        dialog?.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog?.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        return inflater.inflate(layoutResId, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 设置弹窗大小
        dialog?.window?.setLayout(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )

        // 设置蒙层透明度
        dialog?.window?.setDimAmount(0.6f)

        // 初始化视图
        setupViews(view)

        // 加载SVGA动画
        loadSvgaAnimations(view)

        // 开始倒计时
        startCountdown(view)
    }

    private fun setupViews(view: View) {
        // 设置宝箱图片 - 支持网络图片加载
        val ivTreasureBox = view.findViewById<ImageView>(R.id.iv_treasure_box)
        ivTreasureBox?.let {
            //todo  促销活动，图片下发
            if (!treasureBoxImageUrl.isNullOrEmpty()) {
                // 使用网络图片
                GlideUtils.load(
                    context = requireContext(),
                    url = treasureBoxImageUrl,
                    imageView = it,
                    placeholder = R.drawable.promotion_action_src,
                    error = R.drawable.promotion_action_src
                )
            }
        }

        // 设置金额文本
        val tvAmount = view.findViewById<TextView>(R.id.tv_amount)
        tvAmount?.text = amount

        // 设置额外金额文本
        val tvAddAmount = view.findViewById<TextView>(R.id.tv_add_amount)
        tvAddAmount?.text = addAmount

        // 设置描述文本
  /*      val tvDescription = view.findViewById<TextView>(R.id.tv_title)
        if (description.isEmpty()) {
            tvDescription.visibility = View.GONE
        } else {
            tvDescription.visibility = View.VISIBLE
            tvDescription?.text = description
        }*/
        // 设置价格信息
        setupPriceInfo()

        val price_layout = view.findViewById<LinearLayout>(R.id.price_layout)
        price_layout.click {
            onButtonClickListener?.invoke()
            dismiss()
        }


        // 设置活动天数 - 仅在 dialog_promotion1.xml 中存在
        val tvDay = view.findViewById<TextView>(R.id.tv_day)
        tvDay?.let {
            if (remainingCount > 0) {
                it.text = "Only $remainingCount left"
                it.visibility = View.VISIBLE
            } else {
                it.visibility = View.GONE
            }
        }



        // 设置关闭按钮点击事件
        val ivClose = view.findViewById<ImageView>(R.id.iv_close)
        ivClose?.click {
            dismiss()
        }
    }

    private fun setupPriceInfo() {
        // 设置价格文本 - 现价
        val tvPrice = view?.findViewById<TextView>(R.id.tv_price)
        tvPrice?.text = price

        // 设置原价文本 - 原价
        val tvOldPrice = view?.findViewById<TextView>(R.id.tv_old_price)
        // 设置旧价格信息
        setupOldPrice(
            priceTextView = tvOldPrice
        )
    }

    /**
     * 设置旧价格的显示和下划线
     */
    private fun setupOldPrice(priceTextView: TextView?) {
        if (priceTextView == null) return
        
        if (oldPrice.isNotEmpty() && CustomUtils.comparePrice(oldPrice, price) > 0) {
            priceTextView.text = oldPrice
            priceTextView.paint.isStrikeThruText = true
            priceTextView.visibility = View.VISIBLE
        } else {
            priceTextView.text = ""
            priceTextView.paint.isStrikeThruText = false
            priceTextView.visibility = View.GONE
        }
    }

    private fun loadSvgaAnimations(view: View) {
        val parser = SVGAParser(requireContext())

        // 加载按钮动画 - 使用 btn_promotion.svga
        val svgaButton = view.findViewById<AlphaSVGAImageView>(R.id.svga_button)
        svgaButton.click {
            onButtonClickListener?.invoke()
            dismiss()
        }
        CustomUtils.playSvga(svgaButton, "btn_promotion.svga")

        // 加载折扣标签动画 - 仅在 dialog_promotion.xml 中存在
        val svgaDiscount = view.findViewById<AlphaSVGAImageView>(R.id.svga_discount)
        svgaDiscount?.let {
            CustomUtils.playSvga(it, "discount_tags.svga")
        }
    }

    private fun startCountdown(view: View) {
        // 使用新的倒计时布局：tv_hour, tv_min, tv_seconds
        val tvHour = view.findViewById<TextView>(R.id.tv_hour)
        val tvMin = view.findViewById<TextView>(R.id.tv_min)
        val tvSeconds = view.findViewById<TextView>(R.id.tv_seconds)

        // 如果倒计时控件不存在，直接返回
        if (tvHour == null || tvMin == null || tvSeconds == null) {
            return
        }

        // 使用CountdownManager启动倒计时
        startCountdownWithManager(tvHour, tvMin, tvSeconds)
    }



    /**
     * 使用CountdownManager启动倒计时
     */
    private fun startCountdownWithManager(tvHour: TextView, tvMin: TextView, tvSeconds: TextView) {
        try {
            // 移除之前的监听者
            countdownListenerId?.let { listenerId ->
                CountdownManager.removeListener(activityType!!, activityId!!, listenerId)
            }

            // 从CountdownManager获取当前剩余时间
            val currentRemainingTime = CountdownManager.getCurrentRemainingTime(activityType!!, activityId!!)

            if (currentRemainingTime <= 0) {
                tvHour.text = "00"
                tvMin.text = "00"
                tvSeconds.text = "00"
                Timber.tag("PromotionDialog").d("活动已过期: $activityType, $activityId")
                return
            }

            // 使用CountdownManager启动倒计时
            countdownListenerId = CountdownManager.startCountdown(
                activityType = activityType!!,
                activityId = activityId!!,
                onTick = { remainingMillis ->
                    lifecycleScope.launch(Dispatchers.Main) {
                        try {
                            if (isAdded && !isDetached && view != null) {
                                val remainingSeconds = remainingMillis / 1000
                                val hours = remainingSeconds / 3600
                                val minutes = (remainingSeconds % 3600) / 60
                                val seconds = remainingSeconds % 60

                                tvHour.text = String.format("%02d", hours)
                                tvMin.text = String.format("%02d", minutes)
                                tvSeconds.text = String.format("%02d", seconds)
                            }
                        } catch (e: Exception) {
                            Timber.tag("PromotionDialog").e(e, "更新倒计时UI失败")
                        }
                    }
                },
                onFinish = {
                    lifecycleScope.launch(Dispatchers.Main) {
                        try {
                            if (isAdded && !isDetached && view != null) {
                                tvHour.text = "00"
                                tvMin.text = "00"
                                tvSeconds.text = "00"
                            }
                        } catch (e: Exception) {
                            Timber.tag("PromotionDialog").e(e, "倒计时结束UI更新失败")
                        }
                    }
                }
            )

            Timber.tag("PromotionDialog").d("使用CountdownManager启动倒计时: $activityType, $activityId, 剩余时间: ${currentRemainingTime}ms")
        } catch (e: Exception) {
            Timber.tag("PromotionDialog").e(e, "CountdownManager倒计时启动失败")
            tvHour.text = "00"
            tvMin.text = "00"
            tvSeconds.text = "00"
        }
    }



    override fun onDestroyView() {
        // 移除CountdownManager监听者
        if (activityType != null && !activityId.isNullOrEmpty() && countdownListenerId != null) {
            try {
                CountdownManager.removeListener(activityType!!, activityId!!, countdownListenerId!!)
                Timber.tag("PromotionDialog").d("移除CountdownManager监听者: $activityType, $activityId")
            } catch (e: Exception) {
                Timber.tag("PromotionDialog").e(e, "移除CountdownManager监听者失败")
            }
        }

        // 清理资源
        countdownListenerId = null

        Timber.tag("PromotionDialog").d("清理倒计时资源")
        super.onDestroyView()
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        // 通知父Fragment弹窗已关闭
        parentFragmentManager.setFragmentResult("PromotionDialog_dismissed", Bundle())
    }
}