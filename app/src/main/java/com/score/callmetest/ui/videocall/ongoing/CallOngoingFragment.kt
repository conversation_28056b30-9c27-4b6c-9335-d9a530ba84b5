package com.score.callmetest.ui.videocall.ongoing

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Context
import android.graphics.Rect
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.text.Spannable
import android.text.SpannableString
import android.text.Spanned
import android.text.method.ScrollingMovementMethod
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.TextureView
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import androidx.core.graphics.toColorInt
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.score.callmetest.CallmeApplication
import com.score.callmetest.HangupScene
import com.score.callmetest.R
import com.score.callmetest.SceneSource
import com.score.callmetest.databinding.FragmentCallOngoingBinding
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.entity.VideoCallMessageEntity
import com.score.callmetest.manager.DualChannelEventManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.HangUpReason
import com.score.callmetest.manager.LogReportManager
import com.score.callmetest.manager.OnGiftAskMessage
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.manager.VideoCallManager
import com.score.callmetest.manager.VideoCallState
import com.score.callmetest.network.GiftInfo
import com.score.callmetest.network.LiveCallAction
import com.score.callmetest.network.LiveCallExt
import com.score.callmetest.ui.base.BaseFragment
import com.score.callmetest.ui.videocall.CallEndEvaluateFragmentDialog
import com.score.callmetest.ui.videocall.CallEndFreeFragmentDialog
import com.score.callmetest.ui.videocall.CallIncomingManager
import com.score.callmetest.ui.videocall.GiftDialogFragment
import com.score.callmetest.ui.videocall.GiftSendEvent
import com.score.callmetest.ui.videocall.VideoCallActivity
import com.score.callmetest.ui.videocall.ongoing.adapter.VideoCallMessageAdapter
import com.score.callmetest.ui.videocall.ongoing.manager.DualChannelMessageManager
import com.score.callmetest.ui.videocall.ongoing.manager.MessageBarVisibilityManager
import com.score.callmetest.ui.widget.BaseCustomDialog
import com.score.callmetest.ui.widget.CoinRechargeDialog
import com.score.callmetest.ui.widget.decoration.SpaceVerticalItemDecoration
import com.score.callmetest.util.AgodaUtils
import com.score.callmetest.util.AnimatorUtil
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.DeviceUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.ToastUtils
import com.score.callmetest.util.click
import com.score.callmetest.util.keyboard.KeyboardHeightObserver
import com.score.callmetest.util.keyboard.KeyboardHeightProvider
import io.agora.rtc2.video.VideoCanvas
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 视频通话中界面
 */
class CallOngoingFragment(
    val channelName: String?,
    val fromUserId: String?,
    val toUserId: String?,
    var freeCallDuration: Int = 0,
    val rtcToken: String? = null,
) : BaseFragment<FragmentCallOngoingBinding, CallOngoingViewModel>() {
    private var localView: TextureView? = null
    private var remoteView: TextureView? = null

    private var availableCoins = UserInfoManager.myUserInfo?.availableCoins

    private var coinCountdownHandler: Handler? = null
    private var coinCountdownRunnable: Runnable? = null
    private var estimatedHangUpTime: Long = 0

    private var freeCallDurationCountdownHandler: Handler? = null
    private var freeCallDurationCountdownRunnable: Runnable? = null

    // 聊天相关
    private lateinit var messageAdapter: VideoCallMessageAdapter
    private lateinit var dualChannelMessageManager: DualChannelMessageManager
    private lateinit var messageBarVisibilityManager: MessageBarVisibilityManager
    private val messageList = mutableListOf<VideoCallMessageEntity>()

    // 礼物动画相关
    private val DELAY_FADE_OUT = 1000L
    private val FADE_DURATION = 600L
    private val ROTATE_DURATION = 3000L
    private val mScaleRunner = Runnable {
        mScaleAnimatorSet = AnimatorUtil.scale(binding.giftAnimationImage) {
            binding.giftAnimationImage.postOnAnimationDelayed(mFadeOutRunner, DELAY_FADE_OUT)
            binding.giftAnimationBgImage.postOnAnimationDelayed(
                mRotateAndFadeoutRunnable,
                DELAY_FADE_OUT
            )
        }
    }
    private val mFadeOutRunner = Runnable {
        mFadeOutAnimator = AnimatorUtil.fadeOut(binding.giftAnimationImage, FADE_DURATION) {
            // 礼物动画结束
            cancelGiftImageAnimations()
            // 避免还在显示手动调用gone
            binding.giftAnimationImage.visibility = View.GONE
        }
    }
    private val mRotateRunner = Runnable {
        mRotateAnimator =
            AnimatorUtil.rotate(binding.giftAnimationBgImage, duration = ROTATE_DURATION)
    }
    private val mRotateAndFadeoutRunnable = Runnable {
        mRotateAndFadeoutAnimator = AnimatorUtil.rotateAndFadeOut(
            binding.giftAnimationBgImage,
            fadeDuration = FADE_DURATION,
            rotateDuration = ROTATE_DURATION
        ) {
            // 礼物背景动画结束
            cancelGiftBgAnimations()
            // 避免还在显示手动调用gone
            binding.giftAnimationBgImage.visibility = View.GONE
        }
    }

    private var mScaleAnimatorSet: AnimatorSet? = null
    private var mFadeOutAnimator: ObjectAnimator? = null
    private var mRotateAnimator: ObjectAnimator? = null
    private var mRotateAndFadeoutAnimator: AnimatorSet? = null

    fun getVideoActivity(): VideoCallActivity? {
        return activity as? VideoCallActivity
    }

    override fun getViewModelClass() = CallOngoingViewModel::class.java
    override fun getViewBinding(inflater: LayoutInflater, container: ViewGroup?) =
        FragmentCallOngoingBinding.inflate(inflater, container, false)

    override fun initView() {
        GlobalManager.addViewStatusBarTopMargin(requireActivity(), binding.infoLayout)
        GlobalManager.addViewStatusBarTopMargin(requireActivity(), binding.ivQuit)
        GlobalManager.addViewStatusBarTopMargin(requireActivity(), binding.localVideoContainerParent)
        if (!AgodaUtils.isInitialized()) {
//            ToastUtils.showToast("Agora not initialized, please login again")
//            ToastUtils.showToast(CallmeApplication.context.getString(R.string.sys_error_to_login))
//            CustomUtils.logoutAndClearData(context = requireActivity())
            AgodaUtils.initializeGlobal(CallmeApplication.context)
        }
        // 再次初始化后也不行，则退出
        if (!AgodaUtils.isInitialized()) {
            ToastUtils.showToast("Service not initialized, please try it later")
            requireActivity().finish()
            return
        }

        if (!CallIncomingManager.enterChannel()) {
            if (rtcToken != null) {
                AgodaUtils.joinChannelWithUserAccount(
                    rtcToken,
                    channelName!!,
                    fromUserId.toString()
                )
            }
        }

        LogReportManager.reportLiveCallEvent(
            channelName = channelName,
            action = LiveCallAction.ENTER,
            ext = LiveCallExt.CHATTING,
        )


        // 标记进入通话状态，如果还没有标记通话开始时间，则标记（适用于拨出通话）
        getVideoActivity()?.getVideoCallViewModel()?.let { videoCallViewModel ->
            if (!videoCallViewModel.hasEnteredOngoing) {
                videoCallViewModel.markCallStart()
                videoCallViewModel.hasEnteredOngoing = true
            }
        }

        // 标记进入通话状态，如果还没有标记通话开始时间，则标记（适用于拨出通话）
        getVideoActivity()?.getVideoCallViewModel()?.let { videoCallViewModel ->
            if (!videoCallViewModel.hasEnteredOngoing) {
                videoCallViewModel.markCallStart()
                videoCallViewModel.hasEnteredOngoing = true
            }
        }

        if (StrategyManager.isReviewPkg()) {
            binding.videoCoin.visibility = View.GONE
            binding.videoGift.visibility = View.GONE
        }

        // 强制拉最新信息，确认金币数量
        UserInfoManager.refreshMyUserInfo()

        refreshUi()
        showLocalVideo()
        setupDualChannelMessageSystem()
        setupMessageRecyclerView()

        showFreeDurationCountdown(freeCallDuration)
        // todo--dsc 测试取消声音-静音
//        AgodaUtils.muteLocalAudioStream(true)
//        AgodaUtils.muteAllRemoteAudioStreams(true)
        // 如果Activity已经获取到了，就直接用，没有的话再次监听回调
        if (AgodaUtils.remoteUid != 0) {
            showRemoteVideo(AgodaUtils.remoteUid)
//            AgodaUtils.muteRemoteAudioStream(AgodaUtils.remoteUid, true)
        } else {
            // 监听远端用户加入，动态添加远端视频View
            AgodaUtils.observeUserJoined(this) { event ->
                showRemoteVideo(event.uid)
//                AgodaUtils.muteRemoteAudioStream(event.uid, true)
            }
        }

        AgodaUtils.observeUserOffline(this) { event ->
            hangUpCall(
                hangUpReason = HangUpReason.CL_REMOTE_USER_LEFT,
                remark = null
            )
        }

        // 让输入框支持内部滑动
        binding.etInput.setMovementMethod(ScrollingMovementMethod.getInstance())
        binding.etInput.setOnTouchListener { v, event ->
            v.parent.requestDisallowInterceptTouchEvent(true)
            false
        }

        // 输入框焦点监听
        binding.etInput.setOnFocusChangeListener { _, hasFocus ->
            if (::messageBarVisibilityManager.isInitialized) {
                if (hasFocus) {
                    messageBarVisibilityManager.onInputFocused()
                } else {
                    messageBarVisibilityManager.onInputBlurred()
                }
            }
        }
        // 点击远端视频区域收起键盘并切换消息栏
        binding.remoteVideoContainer.click {
            handleScreenClick()
        }
        // 点击根布局收起键盘并切换消息栏
        binding.root.click {
            handleScreenClick()
        }

        updateBoardContainerHeight()
    }

    fun refreshUi() {
        UserInfoManager.getUserInfo(toUserId!!,scope = lifecycleScope) { userInfo ->
            viewModel.userInfo = userInfo
            GlideUtils.load(binding.ivAnchorAvatar, userInfo?.avatarUrl)
            binding.tvAnchorName.text = CustomUtils.truncateName(userInfo?.nickname ?: "")
            GlobalManager.setViewRoundBackground(binding.infoLayout, "#44000000".toColorInt())
            GlobalManager.setViewRoundBackground(binding.etInput, "#44000000".toColorInt())
            updateFreeCallDurationCountdown(freeCallDuration)

            if (userInfo?.isFriend == true) {
                binding.ivFollow.visibility = View.GONE
            } else {
                binding.ivFollow.visibility = View.VISIBLE
            }

            // 更新消息适配器的用户信息，用于自动翻译判断
            if (::messageAdapter.isInitialized) {
                messageAdapter.updateTargetUserInfo(userInfo)
            }
        }
    }

    override fun initListener() {
        setupButtonListeners()

        // 使用双通道事件管理器监听事件（自动去重）
        setupDualChannelEventListeners()

        // 设置礼物索取监听器
        setupGiftRequestListener()

        // 消息监听已在 setupDualChannelMessageSystem 中设置
    }

    private fun setupButtonListeners() {
        // 挂断按钮
        binding.ivQuit.click {
            hideKeyboard()
            BaseCustomDialog(
                context = binding.ivQuit.context,
                emojiResId = R.drawable.emoji_cry,
                title = "Are you sure you want to hang up the phone?",
                agreeText = "Sure",
                cancelText = "Cancel",
                onAgree = {
                    hangUpCall(
                        hangUpReason = HangUpReason.NORMAL,
                        remark = HangupScene.CHATTING_HANGUP
                    )
                },
            ).show()
        }
        binding.camera.click {
            hideKeyboard()
            switchCamera()
        }
        binding.ivFollow.click {
            hideKeyboard()
            viewModel.addFriendWithCallback { success, msg ->
                lifecycleScope.launch(Dispatchers.Main) {
                    if (success) {
                        // 先播放关注成功动画
                        binding.svgaFollow.visibility = View.VISIBLE
                        binding.ivFollow.visibility = View.GONE
                        CustomUtils.playSvga(binding.svgaFollow, "svga_follow_true.svga", loops = 1, onFinished = {
                            // 动画播放完成后显示Toast并隐藏按钮
                            ToastUtils.showToast(CallmeApplication.context.getString(R.string.follow_success))
                            binding.svgaFollow.visibility = View.GONE
                        })
                        viewModel.userInfo?.isFriend = true
                    } else {
                        ToastUtils.showToast(CallmeApplication.context.getString(R.string.follow_failed))
                        viewModel.userInfo?.isFriend = false
                    }
                }
            }
        }

        // 金币充值弹框
        binding.videoCoin.click {
            CoinRechargeDialog(RechargeSource.CHATTING).show(childFragmentManager, "coin_recharge")
        }
        // 礼物弹框
        binding.videoGift.click {
            GiftDialogFragment("call_ongoing").show(childFragmentManager, "gift_dialog")
        }

        // 发送消息按钮
        binding.ivSend.click {
            sendMessage()
        }

        // 输入框回车发送
        binding.etInput.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_SEND) {
                sendMessage()
                true
            } else {
                false
            }
        }

        if (StrategyManager.isReviewPkg()) {
            binding.videoCoin.visibility = View.GONE
            binding.videoGift.visibility = View.GONE
        }
    }

    private var hasHangUp: Boolean = false
    private fun hangUpCall(
        hangUpReason: HangUpReason,
        remark: String?
    ) {
        if (hasHangUp) {
            return
        }
        hasHangUp = true

        // 上报挂断日志
        val hangUpReasonStr = when (hangUpReason) {
            HangUpReason.NORMAL -> "normal"
            HangUpReason.NO_COINS -> "no_coins"
            HangUpReason.CL_EXCEPTION -> "client_exception"
            HangUpReason.CL_CALL_TIMEOUT -> "call_timeout"
            HangUpReason.CL_REMOTE_USER_LEFT -> "remote_user_left"
            HangUpReason.SER_EXCEPTION -> "server_exception"
            HangUpReason.NET_EXCEPTION -> "network_exception"
        }

        LogReportManager.reportLiveCallEvent(
            channelName = channelName,
            action = LiveCallAction.HANGUP,
            ext = LiveCallExt.CHATTING,
            ext2 = hangUpReasonStr
        )

        // 保存通话记录
        saveCallHistoryOnEnd()

        // 调用后端挂断接口
        val channelName = viewModel.channelName ?: ""
        val oppositeUserId = viewModel.toUserId ?: ""
        VideoCallManager.hangUp(
            scope = this.lifecycleScope,
            channelName = channelName,
            hangUpReason = hangUpReason,
            oppositeUserId = oppositeUserId,
            remark = remark,
            onSuccess = {
//                ToastUtils.showToast(CallmeApplication.context.getString(R.string.call_finish))
                if (StrategyManager.isReviewPkg()) {
                    requireActivity().finish()
                } else {
                    showEndDialog()
                }
            },
            onError = { errorMsg ->
                ToastUtils.showToast(errorMsg)
                if (StrategyManager.isReviewPkg()) {
                    requireActivity().finish()
                } else {
                    showEndDialog()
                }
            })
    }

    fun showEndDialog() {
        if (viewModel.userInfo != null) {
            getVideoActivity()?.state = VideoCallState.ENDED
            val availableCoins = UserInfoManager.myUserInfo?.availableCoins ?: 0
            val unitPrice = viewModel.userInfo?.unitPrice ?: 0

            if (availableCoins >= unitPrice) {
                // 金币大于主播每分钟价格展示评价
                CallEndEvaluateFragmentDialog(
                    userInfo = viewModel.userInfo!!,
                    channelName = channelName!!
                ) { dialog ->
                    requireActivity().finish()
                }.show(childFragmentManager, "call_end")
            } else {
                // 其他情况展示金币弹框
                CallEndFreeFragmentDialog(userInfo = viewModel.userInfo!!) { dialog ->
                    requireActivity().finish()
                }.show(childFragmentManager, "call_end")
            }
        } else {
            // 没有主播信息直接就退出不用
            requireActivity().finish()
        }

    }

    // 切换摄像头
    fun switchCamera() {
        AgodaUtils.switchCamera()
    }

    /**
     * 通话结束时保存通话记录
     */
    private fun saveCallHistoryOnEnd() {
        getVideoActivity()?.getVideoCallViewModel()?.let { videoCallViewModel ->
            val callDuration = videoCallViewModel.markCallEnd()

            // 根据通话来源确定通话类型
            // 如果是从VideoCallActivity启动的，需要判断是拨出还是来电
            val activity = requireActivity() as? VideoCallActivity
            val callType = when (activity?.state) {
                com.score.callmetest.manager.VideoCallState.OUTGOING -> {
                    // 拨出通话且成功接通
                    com.score.callmetest.CallType.OUTGOING_CALL
                }

                com.score.callmetest.manager.VideoCallState.INCOMING -> {
                    // 来电且成功接通
                    com.score.callmetest.CallType.INCOMING_CALL
                }

                else -> {
                    // 默认为拨出通话
                    com.score.callmetest.CallType.OUTGOING_CALL
                }
            }

            // 保存通话记录，传递通话类型和时长
            videoCallViewModel.saveCallHistory(callType, callDuration)
        }
    }

    override fun initData() {
        // 将参数赋值给ViewModel
        viewModel.channelName = channelName
        if (fromUserId == UserInfoManager.myUserInfo?.userId) {
            viewModel.fromUserId = fromUserId
            viewModel.toUserId = toUserId
        } else {
            viewModel.fromUserId = toUserId
            viewModel.toUserId = fromUserId
        }
    }

    /**
     * 设置双通道事件监听器
     * 使用DualChannelEventManager统一管理Socket和融云事件，自动去重
     */
    private fun setupDualChannelEventListeners() {
        // 监听挂断事件（双通道去重）
        DualChannelEventManager.observeOnHangUp(this) { hangUpMessage ->
            if (hangUpMessage.channelName == channelName) {
                hangUpCall(
                    hangUpReason = HangUpReason.CL_REMOTE_USER_LEFT,
                    remark = null
                )
            }
        }

        // 监听金币余额事件（双通道去重）
        DualChannelEventManager.observeAvailableCoins(this) { availableCoinsMessage ->
            availableCoins = availableCoinsMessage.coins
            UserInfoManager.myUserInfo?.availableCoins = availableCoins!!
        }

        // 监听预计挂断时间事件（双通道去重）
        DualChannelEventManager.observeEstimatedHangUpTime(this) { estimatedHangUpTimeMessage ->
            estimatedHangUpTime = estimatedHangUpTimeMessage.estimateTime!!
            if (estimatedHangUpTime in 1..60) {
                showCoinNotEnoughCountdown(estimatedHangUpTime)
            } else {
                hideCoinNotEnoughCountdown()
            }
        }

        // 监听礼物索要事件（仅融云）
        DualChannelEventManager.observeOnGiftAsk(this) { giftAskMessage ->
            handleGiftAskMessage(giftAskMessage)
        }

        // 监听礼物发送事件
        EventBus.observe(this, GiftSendEvent::class.java) { giftEvent ->
            // 收到发送礼物指令，执行赠送逻辑
            viewModel.sendGift(giftEvent.gift, SceneSource.CALL_GIFT_PANEL, channelName)
        }

        DualChannelEventManager.observeFreeCallDuration(this) { event ->
            freeCallDuration = event.freeCallDuration
            if (freeCallDuration in 1..20) {
                showFreeDurationCountdown(freeCallDuration)
            } else {
                hideFreeDurationCountdown()
            }
        }
    }

    /**
     * 设置礼物索取监听器
     */
    private fun setupGiftRequestListener() {
        viewModel.setGiftRequestListener(
            onShow = { giftRequestInfo ->
                showGiftRequestBanner(giftRequestInfo)
            },
            onHide = {
                hideGiftRequestBanner()
            }
        )

        // 设置礼物赠送监听器
        viewModel.setGiftSendListener(
            onSuccess = { giftInfo ->
                // 关闭礼物弹框
                childFragmentManager.findFragmentByTag("gift_dialog")?.let { fragment ->
                    if (fragment is GiftDialogFragment && fragment.isVisible) {
                        fragment.dismissAllowingStateLoss()
                    }
                }
                // 礼物赠送成功
                Timber.d("礼物赠送成功: ${giftInfo.giftDesc}")
                // 显示礼物发送横幅
                viewModel.showGiftSendBanner(giftInfo)
                // 展示礼物动画
                showGiftAnimation(giftInfo)
            },
            onError = { errorMsg ->
                // 礼物赠送失败，显示错误提示
                Timber.e("礼物赠送失败: $errorMsg")
            }
        )

        // 设置礼物发送横幅监听器
        viewModel.setGiftSendBannerListener(
            onShow = { giftSendInfo ->
                showGiftSendBanner(giftSendInfo)
            },
            onHide = {
                hideGiftSendBanner()
            }
        )
    }

    /**
     * 显示礼物索取横幅
     * @param giftRequestInfo 礼物索取信息
     */
    private fun showGiftRequestBanner(giftRequestInfo: GiftRequestInfo) {
        try {
            val giftInfo = giftRequestInfo.giftInfo
            // 填充数据到ll_gift_request布局
            binding.llGiftRequest.visibility = View.VISIBLE

            // 设置用户头像
            GlideUtils.load(
                binding.ivAnchorAvatarRequest,
                viewModel.userInfo?.avatarThumbUrl ?: viewModel.userInfo?.avatarUrl,
                R.drawable.placeholder,
                R.drawable.placeholder,
                isCircle = true
            )

            // 设置请求文本
            val requestText = getString(R.string.ask_gift, giftRequestInfo.fromUserName)
            binding.tvGiftRequestText.text = requestText
            // 强制跑马灯
            binding.tvGiftRequestText.isSelected = true
            binding.tvGiftRequestText.setHorizontallyScrolling(true)

            // 设置礼物图标
            giftInfo.let {
//                val uri = it.iconThumbPath?:it.iconPath?: CustomUtils.getGiftResIdByName(it.code?:"")
//                GlideUtils.load(requireContext(), uri, binding.ivGiftIconRequest)
                // 确定只使用本地礼物资源
                val uri = CustomUtils.getGiftResIdById(it.code ?: "")
                binding.ivGiftIconRequest.setImageResource(uri)
            }

            // 设置发送按钮点击事件
            binding.btnGiftSendRequest.click {
                onGiftSendButtonClicked(giftRequestInfo)
            }

            // 添加淡入动画
            binding.llGiftRequest.alpha = 0f
            binding.llGiftRequest.animate()
                .alpha(1f)
                .setDuration(300)
                .start()

            Timber.d("显示礼物索取横幅: ${giftInfo.giftDesc}")

        } catch (e: Exception) {
            Timber.e(e, "显示礼物索取横幅失败")
        }
    }

    private fun updateFreeCallDurationCountdown(time: Int) {
        if (viewModel.userInfo == null) {
            return
        }
        val price = viewModel.userInfo?.unitPrice
        var durationText = ""
        if (time > 0) {
            val freeTimes = "$time"
            durationText = "After $freeTimes S, you will spend icon${price} /min"
            var spannable = CustomUtils.createCoinSpannableText(
                requireContext(),
                durationText,
                alignment = android.text.style.ImageSpan.ALIGN_CENTER,
                spacesBefore = 0,
                spacesAfter = 1
            )
            val start = durationText.indexOf(freeTimes)
            val end = start + freeTimes.length
            if (start >= 0 && freeTimes.isNotEmpty()) {
                spannable.setSpan(
                    ForegroundColorSpan("#FFEB00".toColorInt()),
                    start,
                    end,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
            binding.callDurationText.text = spannable
        } else {
            durationText = "icon$price /min"
            var spannable = CustomUtils.createCoinSpannableText(
                requireContext(),
                durationText,
                alignment = android.text.style.ImageSpan.ALIGN_CENTER,
                spacesBefore = 0,
                spacesAfter = 1
            )
            binding.callDurationText.text = spannable
        }
    }

    /**
     * 隐藏礼物索取横幅
     */
    private fun hideGiftRequestBanner() {
        try {
            if (binding.llGiftRequest.isVisible) {
                // 添加淡出动画
                binding.llGiftRequest.animate()
                    .alpha(0f)
                    .setDuration(300)
                    .withEndAction {
                        binding.llGiftRequest.visibility = View.GONE
                        binding.llGiftRequest.alpha = 1f // 重置透明度
                    }
                    .start()

                Timber.d("隐藏礼物索取横幅")
            }
        } catch (e: Exception) {
            Timber.e(e, "隐藏礼物索取横幅失败")
        }
    }

    /**
     * 处理礼物发送按钮点击
     * 完全参考ChatActivity中的礼物赠送逻辑
     * @param giftRequestInfo 礼物索取信息
     */
    private fun onGiftSendButtonClicked(giftRequestInfo: GiftRequestInfo) {
        try {
            val giftInfo = giftRequestInfo.giftInfo
            // 先验资
            val availableCoins = UserInfoManager.myUserInfo?.availableCoins ?: 0
            if (availableCoins < (giftInfo.coinPrice?.toInt() ?: 0)) {
                // 余额不足---去充值
                ToastUtils.showShortToast(getString(R.string.coins_not_enough))
                CoinRechargeDialog(RechargeSource.VIDEO_ASK_FOR_GIFT).show(childFragmentManager, "coin_recharge")
                return
            }

            // 隐藏横幅
            viewModel.hideGiftRequestBanner()

            // 直接赠送对应的礼物
            viewModel.sendGift(giftInfo, SceneSource.CALL_ASK, channelName)

            Timber.d("点击发送礼物按钮: ${giftInfo.giftDesc}")

        } catch (e: Exception) {
            Timber.e(e, "处理礼物发送按钮点击失败")
        }
    }

    /**
     * 显示礼物发送横幅
     * @param giftSendInfo 礼物发送信息
     */
    private fun showGiftSendBanner(giftSendInfo: GiftSendInfo) {
        try {
            // 填充数据到ll_gift_send布局
            binding.llGiftSend.visibility = View.VISIBLE

            // 设置发送文本 - 使用SpannableString格式化颜色
            binding.tvGiftSendText.text = createFormattedGiftSendMessage(
                giftSendInfo.senderName,
                // 需求确认--不加载礼物名字了
//                getString(R.string.video_send_gift, giftSendInfo.giftInfo.giftDesc)
                getString(R.string.video_send_gift, "")
            )
            // 强制跑马灯
            binding.tvGiftSendText.isSelected = true
            binding.tvGiftSendText.setHorizontallyScrolling(true)

            /* // 设置礼物图标
             if (!giftSendInfo.giftInfo.iconThumbPath.isNullOrEmpty()) {
                 GlideUtils.load(binding.ivGiftSend, giftSendInfo.giftInfo.iconThumbPath)
             } else if (!giftSendInfo.giftInfo.iconPath.isNullOrEmpty()) {
                 GlideUtils.load(binding.ivGiftSend, giftSendInfo.giftInfo.iconPath)
             } else {
                 // 使用本地资源作为备用
                 val localResId = CustomUtils.getGiftResIdByName(giftSendInfo.giftInfo.code ?: "")
                 binding.ivGiftSend.setImageResource(localResId)
             }*/
            // 使用本地资源作为备用（目前只使用本地资源）
            val localResId = CustomUtils.getGiftResIdById(giftSendInfo.giftInfo.code ?: "")
            binding.ivGiftSend.setImageResource(localResId)

            // 设置礼物数量 - 使用SpannableString设置颜色
            val giftNumText = "x${giftSendInfo.count}"
            val spannableNum = SpannableString(giftNumText)
            try {
                val numColor = "#FFFC22".toColorInt()
                spannableNum.setSpan(
                    ForegroundColorSpan(numColor),
                    0,
                    giftNumText.length,
                    SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            } catch (e: Exception) {
                Timber.e(e, "Failed to parse color for gift number")
            }
            binding.tvGiftSendNum.text = spannableNum

            // 添加淡入动画
            binding.llGiftSend.alpha = 0f
            binding.llGiftSend.animate()
                .alpha(1f)
                .setDuration(300)
                .start()

            Timber.d("显示礼物发送横幅: ${giftSendInfo.giftInfo.giftDesc}, 数量: ${giftSendInfo.count}")

        } catch (e: Exception) {
            Timber.e(e, "显示礼物发送横幅失败")
        }
    }

    /**
     * 隐藏礼物发送横幅
     */
    private fun hideGiftSendBanner() {
        try {
            if (binding.llGiftSend.isVisible) {
                // 添加淡出动画
                binding.llGiftSend.animate()
                    .alpha(0f)
                    .setDuration(300)
                    .withEndAction {
                        binding.llGiftSend.visibility = View.GONE
                        binding.llGiftSend.alpha = 1f // 重置透明度
                    }
                    .start()

                Timber.d("隐藏礼物发送横幅")
            }
        } catch (e: Exception) {
            Timber.e(e, "隐藏礼物发送横幅失败")
        }
    }

    /**
     * 创建格式化的礼物发送消息
     * 参考 VideoCallMessageAdapter 中的 SpannableString 使用方式
     * 用户名和冒号：保持原色，消息内容：#FFFC22
     *
     * @param senderName 发送者名称
     * @param content 消息内容
     * @return 格式化的SpannableString
     */
    private fun createFormattedGiftSendMessage(
        senderName: String,
        content: String
    ): SpannableString {
        val fullText = "$senderName: $content"
        val spannableString = SpannableString(fullText)

        try {
            // 用户名和冒号保持原色（不设置颜色）
            // 消息内容使用 #FFFC22 颜色
            val contentColor = "#FFFC22".toColorInt()
            val nameAndColonEndIndex = senderName.length + 1 // 包含冒号
            if (nameAndColonEndIndex < fullText.length) {
                spannableString.setSpan(
                    ForegroundColorSpan(contentColor),
                    nameAndColonEndIndex,
                    fullText.length,
                    SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
        } catch (e: Exception) {
            // 如果颜色解析失败，记录日志但不影响功能
            Timber.e(e, "Failed to parse color for gift send message formatting")
        }

        return spannableString
    }

    /**
     * 处理礼物索要消息
     * @param giftAskMessage 礼物索要消息
     */
    private fun handleGiftAskMessage(giftAskMessage: OnGiftAskMessage) {
        // 委托给ViewModel处理
        viewModel.handleGiftAskMessage(giftAskMessage)
    }

    override fun onDestroyView() {
        super.onDestroyView()

        LogReportManager.reportLiveCallEvent(
            channelName = channelName,
            action = LiveCallAction.EXIT,
            ext = LiveCallExt.CHATTING
        )

        // 更新我得数据
        UserInfoManager.refreshMyUserInfo()
        hangUpCall(hangUpReason = HangUpReason.NORMAL, remark = HangupScene.CHATTING_HANGUP)
        hideCoinNotEnoughCountdown()

        // 清理聊天相关资源，防止内存泄漏
        messageList.clear()
        if (::dualChannelMessageManager.isInitialized) {
            dualChannelMessageManager.cleanup()
        }
        if (::messageBarVisibilityManager.isInitialized) {
            messageBarVisibilityManager.cleanup()
        }

        // 清理礼物动画资源，防止内存泄漏
        cancelAllAnimations()

        hideFreeDurationCountdown()
        freeCallDurationCountdownRunnable = null

        // 清理融云CommandMessage管理器
        // 注意：这里不调用destroy()，因为其他地方可能还在使用
        // RongCloudCommandManager是单例，会在应用退出时统一清理

        // 销毁声网相关 view
        // todo 存在内存泄露，暂时不修改
  /*      AgodaUtils.setupLocalVideo(VideoCanvas(null, VideoCanvas.RENDER_MODE_HIDDEN, 0))
        AgodaUtils.setupRemoteVideo(
            VideoCanvas(
                null,
                VideoCanvas.RENDER_MODE_HIDDEN,
                AgodaUtils.remoteUid
            )
        )
        binding.localVideoContainer.removeAllViews()
        binding.remoteVideoContainer.removeAllViews()*/

        localView = null
        remoteView = null
    }

    private fun showLocalVideo() {
        localView = TextureView(binding.root.context)
        val params = FrameLayout.LayoutParams(
            FrameLayout.LayoutParams.MATCH_PARENT,
            FrameLayout.LayoutParams.MATCH_PARENT
        )
        localView?.layoutParams = params
        binding.localVideoContainer.removeAllViews()
        binding.localVideoContainer.addView(localView)
        AgodaUtils.setupLocalVideo(
            VideoCanvas(
                localView,
                VideoCanvas.RENDER_MODE_HIDDEN,
                0
            )
        )
    }

    private fun showRemoteVideo(uid: Int) {
        remoteView = TextureView(binding.root.context)
        val params = FrameLayout.LayoutParams(
            FrameLayout.LayoutParams.MATCH_PARENT,
            FrameLayout.LayoutParams.MATCH_PARENT
        )
        remoteView?.layoutParams = params
        binding.remoteVideoContainer.removeAllViews()
        binding.remoteVideoContainer.addView(remoteView)
        AgodaUtils.setupRemoteVideo(
            VideoCanvas(
                remoteView,
                VideoCanvas.RENDER_MODE_HIDDEN,
                uid
            )
        )
    }

    private fun showFreeDurationCountdown(time: Int) {
        if (StrategyManager.isReviewPkg()) {
            binding.callDurationText.visibility = View.GONE
            return
        }

        if (!isAdded) return
        binding.callDurationText.visibility = View.VISIBLE
        updateFreeCallDurationCountdown(time)
        freeCallDurationCountdownRunnable?.let {
            freeCallDurationCountdownHandler?.removeCallbacks(it)
        }
        freeCallDurationCountdownHandler = Handler(Looper.getMainLooper())
        var remain = time
        freeCallDurationCountdownRunnable = object : Runnable {
            override fun run() {
                updateFreeCallDurationCountdown(remain)
                if (remain <= 0) {
                    hideFreeDurationCountdown()
                    return
                }
                remain--
                freeCallDurationCountdownHandler?.postDelayed(this, 1000)
            }
        }
        freeCallDurationCountdownHandler?.post(freeCallDurationCountdownRunnable!!)
    }

    private fun showCoinNotEnoughCountdown(time: Long) {
        if (StrategyManager.isReviewPkg()) {
            binding.llCoinNotEnough.visibility = View.GONE
            return
        }

        if (!isAdded) return
        binding.llCoinNotEnough.visibility = View.VISIBLE
        updateCoinCountdownText(time)
        coinCountdownRunnable?.let {
            coinCountdownHandler?.removeCallbacks(it)
        }
        coinCountdownHandler = Handler(Looper.getMainLooper())
        var remain = time
        coinCountdownRunnable = object : Runnable {
            override fun run() {
                if (remain <= 0) {
                    hideCoinNotEnoughCountdown()
                    return
                }
                updateCoinCountdownText(remain)
                remain--
                coinCountdownHandler?.postDelayed(this, 1000)
            }
        }
        coinCountdownHandler?.post(coinCountdownRunnable!!)
        // 关闭按钮
        binding.ivCloseCoinNotEnough.click {
            hideCoinNotEnoughCountdown()
        }

        binding.coinNotEnoughContent.background =
            DrawableUtils.createRoundRectDrawableDp("#853F14FF".toColorInt(), 10f)
        // 充值按钮
        GlobalManager.setViewRoundBackground(binding.btnRecharge, "#2ECF40".toColorInt())
        binding.btnRecharge.click {
            CoinRechargeDialog(RechargeSource.COIN_NOT_ENOUGH).show(childFragmentManager, "coin_recharge")
        }
    }

    private fun updateCoinCountdownText(time: Long) {
        val text = "Video duration remaining: ${time}s"
        val spannable = SpannableString(text)
        val start = text.indexOf(time.toString())
        val end = start + time.toString().length
        spannable.setSpan(
            ForegroundColorSpan(0xFFFFFD65.toInt()),
            start,
            end,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        binding.tvCoinCountdown.text = spannable
    }

    private fun hideFreeDurationCountdown() {
        freeCallDurationCountdownRunnable?.let {
            freeCallDurationCountdownHandler?.removeCallbacks(it)
        }
    }

    private fun hideCoinNotEnoughCountdown() {
        binding.llCoinNotEnough.visibility = View.GONE
        coinCountdownRunnable?.let {
            coinCountdownHandler?.removeCallbacks(it)
        }
    }


    // <editor-folder desc="底部keyboard相关">

    // 键盘高度提供者相关变量
    private var keyboardHeightProvider: KeyboardHeightProvider? = null

    // 键盘高度观察者
    private val mKeyboardHeightObserver = object : KeyboardHeightObserver {
        override fun onKeyboardHeightChanged(orientation: Int, isOpen: Boolean, keyboardHeight: Int) {
            if (isOpen) {
              /*  val outRect = Rect()

              requireActivity().window.decorView.getWindowVisibleDisplayFrame(outRect)
              Timber.d("键盘高度: ${keyboardHeight}")
              Timber.d("bottom: ${outRect.bottom}")
              Timber.d("应用区域高度: ${outRect.height()}")
              Timber.d("屏幕物理高度: ${DeviceUtils.getScreenHeight()}")*/

                val saveKeyBoardHeight = SharePreferenceUtil.getSaveKeyBoardHeight(orientation)
                if (saveKeyBoardHeight != keyboardHeight) {
                    SharePreferenceUtil.saveKeyboardHeight( orientation, keyboardHeight)
                    updateBoardContainerHeight()
                }
                showOrHideBoardContainer(true)
            } else {
                showOrHideBoardContainer(false)
            }
        }
    }


    fun showOrHideBoardContainer(isShow: Boolean){
        binding.boardContainer.isVisible = isShow
    }

    /**
     * 检查是否应该使用键盘高度提供者
     * @return true if should use keyboard height provider
     */
    private fun useKeyboardHeightProvider(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            activity?.isInMultiWindowMode == false
        } else {
            true
        }
    }

    /**
     * 更新面板容器高度
     */
    private fun updateBoardContainerHeight() {
        if (!useKeyboardHeightProvider()) {
            return
        }
        val saveKeyboardHeight = SharePreferenceUtil.getSaveKeyBoardHeight(resources.configuration.orientation)
        val layoutParams = binding.boardContainer.layoutParams ?: return
        if (saveKeyboardHeight <= 0
            && layoutParams.height != resources.getDimensionPixelSize(R.dimen.board_height)
        ) {
            layoutParams.height = resources.getDimensionPixelSize(R.dimen.board_height)
            binding.boardContainer.layoutParams = layoutParams
        } else if (saveKeyboardHeight > 0 && layoutParams.height != saveKeyboardHeight) {
            layoutParams.height = saveKeyboardHeight
            binding.boardContainer.layoutParams = layoutParams
        }
    }

    private fun hideKeyboard() {
        val imm =
            requireContext().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(binding.etInput.windowToken, 0)
        binding.etInput.clearFocus()
        showOrHideBoardContainer(false)
    }

    // </editor-folder>

    /**
     * 设置双通道消息系统
     */
    private fun setupDualChannelMessageSystem() {
        // 获取目标用户信息
        val targetUserName = viewModel.userInfo?.nickname ?: "Unknown"
        val targetUserAvatar = viewModel.userInfo?.avatarUrl ?: ""
        val currentUserId = UserInfoManager.myUserInfo?.userId ?: ""

        // 初始化双通道消息管理器
        dualChannelMessageManager = DualChannelMessageManager(
            currentUserId = currentUserId,
            targetUserId = toUserId ?: "",
            targetUserName = targetUserName,
            targetUserAvatar = targetUserAvatar
        )

        // 设置消息回调
        dualChannelMessageManager.setMessageCallback(object :
            DualChannelMessageManager.MessageCallback {
            override fun onMessageSent(message: VideoCallMessageEntity) {
                // 消息发送成功，已经添加到列表中，无需额外处理
                lifecycleScope.launch(Dispatchers.Main) {
                    // 通知可见性管理器
                    if (::messageBarVisibilityManager.isInitialized) {
                        messageBarVisibilityManager.onMessageSent()
                    }
                }
            }

            override fun onMessageSendFailed(message: VideoCallMessageEntity, error: String) {
                // 消息发送失败，显示错误提示
                Timber.e("消息发送失败: $error")
                ToastUtils.showToast(CallmeApplication.context.getString(R.string.send_failed_try_again))
                // 可以在这里添加重发逻辑或更新消息状态
            }

            override fun onMessageReceived(message: VideoCallMessageEntity) {
                // 接收到新消息，添加到列表中
                lifecycleScope.launch(Dispatchers.Main) {
                    addMessageToList(message)
                    // 通知可见性管理器
                    if (::messageBarVisibilityManager.isInitialized) {
                        messageBarVisibilityManager.onNewMessage()
                    }
                }
            }
        })

        // 开始监听消息
        dualChannelMessageManager.startListening(this)

        // 初始化消息栏可见性管理器
        messageBarVisibilityManager = MessageBarVisibilityManager(binding.rvMessageList)
    }

    /**
     * 设置消息RecyclerView
     */
    private fun setupMessageRecyclerView() {
        messageAdapter = VideoCallMessageAdapter(
            scope = lifecycleScope,
            onMessageClickListener = { message ->
                // 消息点击事件，可以用于显示/隐藏翻译等功能
                // 暂时不需要处理，预留扩展
            },
            targetUserInfo = viewModel.userInfo
        )

        binding.rvMessageList.apply {
            layoutManager = LinearLayoutManager(context).apply {
                // 设置为反向布局，新消息在底部
                reverseLayout = false
                stackFromEnd = true
            }
            adapter = messageAdapter

            // 添加10dp的垂直间距
            val spacing = DisplayUtils.dp2px(10f)
            addItemDecoration(SpaceVerticalItemDecoration(spacing))

            // 设置最大高度为200dp
//            setMaxHeightDp(200f)
        }
    }

    /**
     * 发送消息（双通道）
     */
    private fun sendMessage() {
        val content = binding.etInput.text.toString().trim()

        // 基本验证
        if (content.isBlank()) {
            ToastUtils.showToast(CallmeApplication.context.getString(R.string.text_not_empty))
            return
        }

        if (content.length > 500) {
            ToastUtils.showToast(CallmeApplication.context.getString(R.string.text_too_long))
            return
        }

        // 清空输入框
        binding.etInput.setText("")
        hideKeyboard()

        // 创建发送的消息实体并添加到列表
        val messageId = "msg_${System.currentTimeMillis()}_${(Math.random() * 1000).toInt()}"
        val myUserInfo = UserInfoManager.myUserInfo
        val message = VideoCallMessageEntity.Companion.createSentMessage(
            messageId = messageId,
            senderId = myUserInfo?.userId ?: "",
            senderName = myUserInfo?.nickname ?: "Me",
            senderAvatar = myUserInfo?.avatarUrl ?: "",
            receiverId = toUserId ?: "",
            content = content
        )

        // 先添加到消息列表显示
        addMessageToList(message)

        // 通过双通道发送消息
        dualChannelMessageManager.sendMessage(content)
    }

    // 消息接收处理已移至 DualChannelMessageManager 中

    /**
     * 添加消息到列表并更新UI（线程安全）
     */
    private fun addMessageToList(message: VideoCallMessageEntity) {
        // 确保在主线程中执行UI操作
        lifecycleScope.launch(Dispatchers.Main) {
            synchronized(messageList) {
                messageList.add(message)
                val newList = messageList.toList() // 创建副本避免并发修改

                messageAdapter.submitList(newList) {
                    // 滚动到最新消息
                    if (messageList.isNotEmpty()) {
                        binding.rvMessageList.scrollToBottom()
                    }
                    // 强制刷新itemDecoration
                    binding.rvMessageList.invalidateItemDecorations()
                }
            }
        }
    }

    /**
     * 处理屏幕点击事件
     * 当输入法存在时：隐藏输入法，但不立即隐藏消息栏，而是5秒后消失
     * 当输入法不存在时：切换消息栏显示状态
     */
    private fun handleScreenClick() {
        if (binding.etInput.isFocused) {
            // 输入法存在时，隐藏输入法但不立即隐藏消息栏
            hideKeyboard()
            if (::messageBarVisibilityManager.isInitialized) {
                // 显示消息栏并安排5秒后自动隐藏
                messageBarVisibilityManager.showMessageBar(autoHide = true)
            }
        } else {
            // 输入法不存在时，切换消息栏显示状态
            if (::messageBarVisibilityManager.isInitialized) {
                messageBarVisibilityManager.toggleMessageBar()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        if (useKeyboardHeightProvider()) {
            if(keyboardHeightProvider == null){
                activity?.let {
                    keyboardHeightProvider = KeyboardHeightProvider(it).apply {
                        setKeyboardHeightObserver(mKeyboardHeightObserver)
                    }
                }
            }
            binding.root.post {
                keyboardHeightProvider?.start()
            }
        }
    }

    override fun onStart() {
        super.onStart()
        if(useKeyboardHeightProvider()){
            activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING)
        }else {
            activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        }
    }

    override fun onPause() {
        super.onPause()
        // 停止键盘高度提供者
        keyboardHeightProvider?.stop()
    }

    // <editor-folder desc="礼物动画相关方法">

    /**
     * 展示礼物动画
     * @param giftInfo 礼物信息
     */
    private fun showGiftAnimation(giftInfo: GiftInfo?) {
        if (giftInfo == null) return
        try {
            // 先隐藏，准备动画
            binding.giftAnimationImage.visibility = View.GONE
            binding.giftAnimationBgImage.visibility = View.GONE
            cancelAllAnimations()

            // 使用本地资源(ps.确定只使用本地礼物资源)
            val name = giftInfo.code ?: ""
            val localResId = CustomUtils.getGiftResIdById(name)
            binding.giftAnimationImage.setImageResource(localResId)

            /*// 获取礼物图标URI
            val giftUri = giftInfo.iconThumbPath ?: giftInfo.iconPath

            // 加载图片
            if (!giftUri.isNullOrEmpty()) {
                // 使用网络图片
                GlideUtils.load(binding.giftAnimationImage, giftUri)
            } else {
                // 使用本地资源
                val name = giftInfo.code ?: ""
                val localResId = CustomUtils.getGiftResIdByName(name)
                binding.giftAnimationImage.setImageResource(localResId)
            }*/

            // 启动动画
            binding.giftAnimationBgImage.postOnAnimation(mRotateRunner)
            binding.giftAnimationImage.postOnAnimation(mScaleRunner)

            Timber.d("展示礼物动画: ${giftInfo.giftDesc}")

        } catch (e: Exception) {
            Timber.e(e, "展示礼物动画失败")
        }
    }

    /**
     * 取消所有正在进行的动画
     */
    private fun cancelAllAnimations() {
        cancelGiftImageAnimations()
        cancelGiftBgAnimations()
    }

    /**
     * 取消礼物图片动画
     */
    private fun cancelGiftImageAnimations() {
        mScaleAnimatorSet?.run {
            cancel()
            removeAllListeners()
        }
        mScaleAnimatorSet = null
        mFadeOutAnimator?.run {
            cancel()
            removeAllListeners()
        }
        mFadeOutAnimator = null
        binding.giftAnimationImage.run {
            removeCallbacks(mScaleRunner)
            removeCallbacks(mFadeOutRunner)
            // 确保完全重置视图属性
            scaleX = 1f
            scaleY = 1f
            alpha = 1f
        }
    }

    /**
     * 取消礼物背景动画
     */
    private fun cancelGiftBgAnimations() {
        mRotateAnimator?.run {
            cancel()
            removeAllListeners()
        }
        mRotateAnimator = null
        mRotateAndFadeoutAnimator?.run {
            cancel()
            removeAllListeners()
        }
        mRotateAndFadeoutAnimator = null
        binding.giftAnimationBgImage.run {
            removeCallbacks(mRotateRunner)
            removeCallbacks(mRotateAndFadeoutRunnable)
            // 重置旋转角度
            rotation = 0f
            alpha = 1f
        }
    }

    // </editor-folder>

}