package com.score.callmetest.ui.web

import android.annotation.TargetApi
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.content.pm.ResolveInfo
import android.net.Uri
import android.net.http.SslError
import android.os.Build
import android.text.TextUtils
import android.webkit.SslErrorHandler
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient
import timber.log.Timber
import androidx.core.net.toUri

/**
 * 自定义 WebViewClient，用于处理 WebView 的 URL 加载逻辑
 * 支持处理 intent:// 协议和自定义 scheme
 *
 * 内存泄漏防护：使用弱引用持有Activity，避免内存泄漏
 */
class CustomWebClient(context: Activity) : WebViewClient() {

    // 使用弱引用避免内存泄漏
    private val contextRef = java.lang.ref.WeakReference(context)

    companion object {
        private const val TAG = "CustomWebClient"
        private val HTTP_SCHEMES = listOf("http", "https")

        /**
         * 启动指定 URL
         * @param context 上下文
         * @param url 要启动的 URL
         * @param isNewTask 是否在新任务中启动
         */
        @JvmStatic
        fun startUrl(context: Context?, url: String?, isNewTask: Boolean) {
            if (context != null && !TextUtils.isEmpty(url)) {
                try {
                    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                    if (isNewTask) {
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    }
                    context.startActivity(intent)
                } catch (e: Exception) {
                    Timber.e(e, "Failed to start URL: $url")
                }
            }
        }

        /**
         * 检查是否有指定包名的 Activity 可以处理 Intent
         * @param context 上下文
         * @param intent 要检查的 Intent
         * @param packageName 包名
         * @return 是否有对应的 Activity
         */
        @JvmStatic
        fun hasActivity(context: Context, intent: Intent, packageName: String): Boolean {
            val pm = context.packageManager
            val appList = pm.queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY)

            for (info in appList) {
                if (info.activityInfo.packageName == packageName) {
                    return true
                }
            }
            return false
        }

        /**
         * 启动应用市场
         * @param context 上下文
         * @param url 市场 URL
         * @param forceUseGoogle 是否强制使用 Google Play
         */
        @JvmStatic
        fun startAppMarketWithUrl(context: Context?, url: String?, forceUseGoogle: Boolean) {
            if (context == null || TextUtils.isEmpty(url)) return

            try {
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                if (forceUseGoogle || hasActivity(context, intent, "com.android.vending")) {
                    intent.setPackage("com.android.vending")
                }
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(intent)
            } catch (e: Exception) {
                Timber.e(e, "Failed to start app market with URL: $url")
                try {
                    startUrl(context, url, true)
                } catch (e1: Exception) {
                    Timber.e(e1, "Failed to start URL as fallback: $url")
                }
            }
        }
    }

    override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
        val url = request?.url?.toString()
        Timber.d("shouldOverrideUrlLoading url=$url")
        if (shouldOverrideUrlLoadingInner(view, url)) {
            return true
        }
        return super.shouldOverrideUrlLoading(view, request)
    }

    /**
     * 解析 URL 并使用系统功能打开
     * 处理两种情况：
     * 1. 处理 "intent://xxxx" URL
     * 2. 处理自定义 scheme URL
     *
     * @param view WebView 实例
     * @param url 要处理的 URL
     * @return 是否已处理该 URL
     */
    private fun shouldOverrideUrlLoadingInner(view: WebView?, url: String?): Boolean {
        if (TextUtils.isEmpty(url)) {
            return false
        }

        // 获取Activity引用，如果为null说明Activity已被回收
        val context = contextRef.get() ?: return false

        val uri = url?.toUri()
        uri?.let {
            // 处理 intent:// 协议
            if ("intent" == uri.scheme) {
                try {
                    val intent = Intent.parseUri(uri.toString(), Intent.URI_INTENT_SCHEME)
                    intent?.let { parsedIntent ->
                        val pm = context.packageManager
                        val info =
                            pm.resolveActivity(parsedIntent, PackageManager.MATCH_DEFAULT_ONLY)
                        if (info != null) {
                            // 有对应的应用可以处理，直接启动
                            context.startActivity(
                                Intent.parseUri(
                                    uri.toString(),
                                    Intent.URI_INTENT_SCHEME
                                )
                            )
                            return true
                        } else {
                            // 没有对应应用，尝试使用 fallback URL
                            val fallbackUrl = parsedIntent.getStringExtra("browser_fallback_url")
                            if (!TextUtils.isEmpty(fallbackUrl)) {
                                if (fallbackUrl?.startsWith("market://") == true) {
                                    startAppMarketWithUrl(context, fallbackUrl, false)
                                } else {
                                    view?.loadUrl(fallbackUrl!!)
                                }
                                return true
                            }
                        }
                    }
                } catch (e: Exception) {
                    Timber.e(e, "Failed to parse intent URI: $url")
                }
            }

            // 处理非 HTTP/HTTPS 协议
            if (!HTTP_SCHEMES.contains(uri.scheme)) {
                startUrl(context, url, true)
                return true
            }
        }

        return false
    }

    /**
     * 清理资源，防止内存泄漏
     */
    fun cleanup() {
        contextRef.clear()
    }
}
