package com.score.callmetest.ui.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.RelativeLayout
import androidx.recyclerview.widget.GridLayoutManager
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R
import com.score.callmetest.databinding.RechargeSubViewBinding
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.manager.CountdownManager
import com.score.callmetest.manager.GoodsManager
import com.score.callmetest.manager.RechargeManager
import com.score.callmetest.network.GoodsInfo
import com.score.callmetest.ui.main.GridSpacingItemDecoration
import com.score.callmetest.ui.main.RechargeOption
import com.score.callmetest.ui.main.RechargeOptionAdapter
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.ToastUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

class RechargeSubView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RelativeLayout(context, attrs, defStyleAttr) {
    private var binding: RechargeSubViewBinding =
        RechargeSubViewBinding.inflate(LayoutInflater.from(context), this, true)
    private val viewScope = CoroutineScope(Dispatchers.Main)
    private var loadJob: Job? = null

    // TODO: 展开/收起功能暂时注释，目前只显示6个商品
    // private var isExpanded = false
    private var allRechargeOptions: List<RechargeOption> = emptyList()

    /** 充值成功回调，供外部监听（如关闭 Dialog） */
    var onRechargeClick: (() -> Unit)? = null

    var entry: RechargeSource = RechargeSource.COIN_NOT_ENOUGH
    var bcInvitationId: String? = null

    init {
        binding.rvRechargeOptions.layoutManager = GridLayoutManager(context, 3)
        binding.rvRechargeOptions.addItemDecoration(
            GridSpacingItemDecoration(3, DisplayUtils.dp2pxInternal(9f), true)
        )
        // TODO: 展开/收起功能暂时注释，目前只显示6个商品
        // binding.tvMoreOption.paint.isUnderlineText = true
        // binding.moreOptionLayout.click {
        //     isExpanded = !isExpanded
        //     updateOptionsView()
        // }
        refresh()
    }

    /** 外部可调用，刷新充值选项 */
    fun refresh(updateCallback: () -> Unit = { }) {
        loadJob?.cancel()
        if(!bcInvitationId.isNullOrBlank()){
            // 邀请充值链接
            loadJob = viewScope.launch {
                try {
                    val goodsList = GoodsManager.getBroadcasterInvitationGoods(bcInvitationId!!)
                    updateOptions(goodsList)
                    updateCallback()
                } catch (e: Exception) {
                    ToastUtils.showShortToast(CallmeApplication.context.getString(R.string.net_error_and_try_again))
                }
            }
            return
        }

        val cachedGoods = GoodsManager.getCachedAllGoods()
        if (cachedGoods.isNotEmpty()) {
            updateOptions(cachedGoods)
        } else {
            loadJob = viewScope.launch {
                try {
                    val goodsList = GoodsManager.getAllGoods()
                    updateOptions(goodsList)
                } catch (e: Exception) {
                    ToastUtils.showShortToast(CallmeApplication.context.getString(R.string.net_error_and_try_again))
                }
            }
        }
    }

    private fun updateOptions(goodsList: List<GoodsInfo>) {
        // 先初始化倒计时状态（使用商品促销类型）
        goodsList.forEach { goods ->
            if (goods.type == "1" && !goods.code.isNullOrEmpty()) {
                val remainTime = goods.remainMilliseconds ?: goods.surplusMillisecond
                if (remainTime != null && remainTime > 0) {
                    CountdownManager.initOrUpdateCountdown(
                        CountdownManager.ActivityType.GOODS_PROMOTION,
                        goods.code,
                        remainTime
                    )
                }
            }
        }

        allRechargeOptions = goodsList
            .sortedWith(compareByDescending<GoodsInfo> { it.isPromotion == true }.thenBy {
                it.price ?: Double.MAX_VALUE
            })
            .map { goods ->
                val (priceSymbol, price) = GoodsManager.getLocaleGoodsPrice(goods)
                val (oldPriceSymbol, oldPrice) = GoodsManager.getLocaleGoodsPrice(goods,true)
                RechargeOption(
                    iconRes = GoodsManager.getIconByGoodsId(goods.code),
                    coinAmount = (goods.exchangeCoin ?: 0) ,
                    price = price?.let { "$priceSymbol$it"}?:"",
                    oldPrice = oldPrice?.let { oldPriceSymbol+it }?:"",
                    name = goods.name,
                    extraCoinPercent = goods.extraCoinPercent,
                    bonus = if ((goods.discount
                            ?: 0.0) > 0
                    ) "+${goods.discount?.times(100)}%" else null,
                    goodsCode = goods.code,
                    type = goods.type,
                    remainMilliseconds = goods.remainMilliseconds ?: goods.surplusMillisecond,
                    invitationId = goods.invitationId,
                )
            }
        updateOptionsView()
    }

    private fun updateOptionsView() {
        val distinctOptions = allRechargeOptions
            // 保留了类型为 "0" 的商品，或者类型为 "1" 且剩余时间大于 0 的商品。
            .filter { it.type == "0" || ((it.remainMilliseconds ?: 0L) > 0L) }
            .distinctBy { it.oldPrice }


        binding.rvRechargeOptions.adapter = RechargeOptionAdapter.Companion.createWithLimit(
            options = distinctOptions,
            onClick = { option ->
                option.goodsCode?.let { goodsCode ->
                    ActivityUtils.getTopActivity()?.let {
                        RechargeManager.startRecharge(activity = it, goodsCode = goodsCode, entry = entry,invitationId = option.invitationId
                        , bcInvitationId = bcInvitationId)
                    }
                    onRechargeClick?.invoke()
                }
            },
            onCountdownEnd = { refresh() } // 添加计时结束回调
        )

        // TODO: 展开/收起功能暂时注释，目前只显示6个商品
        // 确保箭头始终为白色
        // binding.tvMoreOptionArrow.setColorFilter(ContextCompat.getColor(context, R.color.white))
        // if (allRechargeOptions.size > 6) {
        //     binding.moreOptionLayout.visibility = VISIBLE
        //     binding.tvMoreOptionArrow.setImageResource(if (isExpanded) R.drawable.arrow_up else R.drawable.arrow_down)
        // } else {
        //     binding.moreOptionLayout.visibility = GONE
        // }

        // 暂时隐藏更多选项按钮
        binding.moreOptionLayout.visibility = GONE
    }

    override fun onDetachedFromWindow() {
        clear()
        super.onDetachedFromWindow()
    }

    fun clear() {
        binding.rvRechargeOptions.adapter = null
    }
} 