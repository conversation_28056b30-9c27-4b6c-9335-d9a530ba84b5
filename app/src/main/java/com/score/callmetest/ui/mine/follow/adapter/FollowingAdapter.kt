package com.score.callmetest.ui.mine.follow.adapter

import android.Manifest
import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.score.callmetest.CallStatus
import com.score.callmetest.CallmeApplication.Companion.context
import com.score.callmetest.Constant
import com.score.callmetest.R
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.databinding.ItemFragmentFollowingBinding
import com.score.callmetest.databinding.ItemListBottomBinding
import com.score.callmetest.manager.AppPermissionManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.SocketManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.BroadcasterModel
import com.score.callmetest.network.FollowModel
import com.score.callmetest.ui.broadcaster.BroadcasterDetailActivity
import com.score.callmetest.ui.mine.follow.BottomState
import com.score.callmetest.ui.videocall.VideoCallActivity
import com.score.callmetest.ui.widget.InsufficientBalanceDialog
import com.score.callmetest.util.CountryUtils
import com.score.callmetest.util.ToastUtils
import com.score.callmetest.util.click
import timber.log.Timber

class FollowingAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    companion object {
        private const val VIEW_TYPE_FOLLOWING = 0
        private const val VIEW_TYPE_BOTTOM = 1
    }

    private val followings = mutableListOf<FollowModel>()
    private var bottomState: BottomState = BottomState.HIDDEN

    // 聊天跳转回调
    private var onChatClickListener: ((FollowModel) -> Unit)? = null

    fun setOnChatClickListener(listener: (FollowModel) -> Unit) {
        this.onChatClickListener = listener
    }

    fun setBottomState(state: BottomState) {
        if (this.bottomState != state) {
            this.bottomState = state
            notifyDataSetChanged()
        }
    }

    fun setData(newFollowings: List<FollowModel>) {
        followings.clear()
        followings.addAll(newFollowings)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_FOLLOWING -> {
                val binding = ItemFragmentFollowingBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false)
                ViewHolder(binding)
            }

            VIEW_TYPE_BOTTOM -> {
                val binding = ItemListBottomBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false)
                BottomViewHolder(binding)
            }
            // todo 异常类型主动报错，后面可以删除
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is ViewHolder && position < followings.size) {
            val followModel = followings[position]
            holder.bind(followModel, position)
        } else if (holder is BottomViewHolder) {
            holder.bind("Bottom")
        }
    }

    private fun startVideoCall(activity: androidx.appcompat.app.AppCompatActivity, followModel: FollowModel) {
        Timber.tag("FollowingAdapter").d("Starting video call with: ${followModel.nickname}")

        // 1. 检查价格是否有效
        val unitPrice = followModel.unitPrice ?: 0
        if (unitPrice < 0) {
            ToastUtils.showShortToast("Invalid price")
            return
        }

        if (!StrategyManager.isReviewPkg()) {
            // 2. 先检查金币是否足够
            val availableCoins = UserInfoManager.myUserInfo?.availableCoins ?: 0
            if (availableCoins < unitPrice) {
                // 弹出金币充值弹窗
                val dialog = InsufficientBalanceDialog.newInstance(unitPrice, RechargeSource.FOLLOW_CALL.value())
                dialog.show(activity.supportFragmentManager, "insufficient_balance")
                return
            }
        }

        // 3. 检查网络连接
        if (!SocketManager.instance.isConnected()) {
            ToastUtils.showToast(context.getString(R.string.long_connection_network_offline))
            return
        }

        // 4. 获取在线状态并发起通话
        UserInfoManager.loadOnlineStatus(
            activity.lifecycleScope, followModel.userId
        ) { status, error ->
            activity.runOnUiThread {
                if (error == null && status != null) {
                    if (CallStatus.ONLINE != status && CallStatus.AVAILABLE != status) {
                        // 状态不可用时显示toast
                        val statusText = CallStatus.getDisplayText(status)
                        val message = context.getString(R.string.user_status_not_available, statusText)
                        ToastUtils.showToast(message)
                        return@runOnUiThread
                    }

                    // 5. 在线状态检查通过后，请求权限
                    AppPermissionManager.checkAndRequestCameraMicrophonePermission(
                        activity,
                        onGranted = {
                            VideoCallActivity.startOutgoing(
                                context = activity,
                                userId = followModel.userId,
                                avatarUrl = followModel.avatarUrl ?: followModel.avatarThumbUrl ?: "",
                                nickname = followModel.nickname ?: "",
                                age = followModel.age?.toString() ?: "",
                                country = followModel.country ?: "",
                                unitPrice = unitPrice.toString()
                            )
                        },
                        onDenied = {
                            ToastUtils.showToast(context.getString(R.string.camera_microphone_permission_required))
                            val shouldShow = AppPermissionManager.shouldShowRequestPermissionRationale(
                                activity,
                                Manifest.permission.RECORD_AUDIO
                            )
                            if (!shouldShow) {
                                // 权限被永久拒绝，跳转设置页面
                                ToastUtils.showToast(context.getString(R.string.camera_microphone_permission_check_hint))
                                AppPermissionManager.openAppSettings(activity)
                            }
                        }
                    )
                } else {
                    ToastUtils.showToast(context.getString(R.string.failed_to_get_user_status))
                }
            }
        }
    }

    /**
     * 跳转到聊天
     */
    private fun startChat(followModel: FollowModel) {
        Timber.tag("FollowingAdapter").d("Starting chat with: ${followModel.nickname}")
        // 通过回调通知Fragment处理聊天跳转
        onChatClickListener?.invoke(followModel)
    }

    override fun getItemCount(): Int = followings.size + if (bottomState != BottomState.HIDDEN) 1 else 0

    override fun getItemViewType(position: Int): Int {
        return if (bottomState != BottomState.HIDDEN && position == followings.size) VIEW_TYPE_BOTTOM else VIEW_TYPE_FOLLOWING
    }

    private fun followModelToBroadcasterModel(followModel: FollowModel): BroadcasterModel {
        return BroadcasterModel(
            userId = followModel.userId,
            nickname = followModel.nickname,
            avatar = followModel.avatar,
            gender = followModel.gender,
            age = followModel.age,
            country = followModel.country,
            status = followModel.onlineStatus ?: CallStatus.OFFLINE,
            callCoins = followModel.unitPrice,
            unit = "min", // 通话单位，默认"min"
            isFriend = followModel.isFollows,
            about = followModel.about,
            grade = followModel.level,
            analysisLanguage = followModel.language,
            isSignBroadcaster = followModel.isSignBroadcaster,
            showRoomVersion = followModel.showRoomVersion,
            broadcasterType = followModel.userType,
            avatarThumbUrl = followModel.avatarThumbUrl,
            isVip = followModel.isVip ?: false,
        )
    }

    inner class ViewHolder(private val binding: ItemFragmentFollowingBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(followModel: FollowModel, position: Int) {
            // 昵称
            binding.tvUsername.text = followModel.nickname ?: ""

            // 国家/地区
            binding.tvRegion.text = followModel.country ?: ""
            binding.ivFlag.setImageResource(CountryUtils.getIconByEnName(followModel.country))

            // 头像
            val avatarUrl = followModel.avatarUrl ?: followModel.avatarThumbUrl ?: ""
            if (avatarUrl.isNotEmpty()) {
                Glide.with(binding.ivAvatar.context)
                    .load(avatarUrl)
                    .placeholder(R.drawable.placeholder)
                    .error(R.drawable.placeholder)
                    .into(binding.ivAvatar)
            } else {
                binding.ivAvatar.setImageResource(R.drawable.placeholder)
            }

            // 设置状态指示器颜色
            val statusColor = GlobalManager.getStatusColor(followModel.onlineStatus?: CallStatus.OFFLINE)
            GlobalManager.setViewRoundBackground(binding.statusIndicator, statusColor)

            // 根据在线状态设置视频通话图标
            if (followModel.onlineStatus == CallStatus.ONLINE) {
                binding.ivVideoIndicator.setImageResource(R.drawable.call_video)
            } else {
                binding.ivVideoIndicator.setImageResource(R.drawable.btn_message)
            }

            // 视频通话图标点击事件
            binding.ivVideoIndicator.click {
                val activity = itemView.context as? androidx.appcompat.app.AppCompatActivity
                if (activity == null) {
                    ToastUtils.showToast(context.getString(R.string.page_error_unable_to_initiate_call))
                    return@click
                }

                if (followModel.onlineStatus == CallStatus.ONLINE) {
                    // 在线状态：发起视频通话
                    startVideoCall(activity, followModel)
                } else {
                    // 其他状态：跳转到聊天
                    startChat(followModel)
                }
            }
            binding.ivAvatar.click {
                ToBroadcasterDetailActivity(itemView.context, position)
            }

            // item点击事件
            itemView.click {
                ToBroadcasterDetailActivity(itemView.context, position)
            }
        }
    }
    // 跳转到主播详情页面
    private fun ToBroadcasterDetailActivity(context: Context, position: Int) {
        if (position != RecyclerView.NO_POSITION && position < followings.size) {
            val followModel = followings[position]
            val broadcasterModel = followModelToBroadcasterModel(followModel)
            val intent = Intent(context, BroadcasterDetailActivity::class.java)
            intent.putExtra(Constant.BROADCASTER_MODEL, broadcasterModel)
            context.startActivity(intent)
        }
    }
    inner class BottomViewHolder(private val binding: ItemListBottomBinding) : RecyclerView.ViewHolder(binding.root) {

        fun bind(text: String) {
            when (bottomState) {
                BottomState.LOADING -> {
                    // 显示加载动画，隐藏静态底部
                    binding.layoutLoading.visibility = android.view.View.VISIBLE
                    binding.layoutFinished.visibility = android.view.View.GONE
                }
                BottomState.FINISHED -> {
                    // 隐藏加载动画，显示静态底部
                    binding.layoutLoading.visibility = android.view.View.GONE
                    binding.layoutFinished.visibility = android.view.View.VISIBLE
                    binding.tvBottomText.text = text
                }
                BottomState.HIDDEN -> {
                    // 隐藏所有（这种情况下不应该调用bind，但为了安全起见）
                    binding.layoutLoading.visibility = android.view.View.GONE
                    binding.layoutFinished.visibility = android.view.View.GONE
                }
            }
        }
    }

} 