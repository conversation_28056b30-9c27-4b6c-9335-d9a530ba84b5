package com.score.callmetest.ui.chat.adapter

import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemChatMessageGiftBinding
import com.score.callmetest.entity.ChatMessageEntity
import com.score.callmetest.entity.MessageStatus
import com.score.callmetest.network.GiftInfo
import com.score.callmetest.util.ClickUtils
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.click

/**
 * 礼物消息ViewHolder
 */
internal class GiftMessageViewHolder(
    private val binding: ItemChatMessageGiftBinding,
    private val mChatMessageListeners: ChatAdapterListeners
) : RecyclerView.ViewHolder(binding.root),MessageHolder {

    private var mCurrentMessage: ChatMessageEntity? = null

    private var mGiftInfo: GiftInfo? = null

    init {
        // 设置点击事件
        ClickUtils.setOnGlobalDebounceClickListener(binding.root) {
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnMessageClickListener?.invoke(message, binding.root)
            }
        }

        binding.root.setOnLongClickListener {
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnMessageLongClickListener?.invoke(message, binding.root) ?: false
            } ?: false
        }
    }

    override fun bind(message: ChatMessageEntity) {
        mCurrentMessage = message
        mGiftInfo = message.giftInfo

        // gift-num
        fillGiftNum(message)


        /*val giftUri = mGiftInfo?.iconThumbPath ?: mGiftInfo?.iconPath ?: run {
            // 通过name来寻找本地png
            val name = mGiftInfo?.code ?: ""
            CustomUtils.getGiftResIdByName(name)
        }*/
        // 确定只使用本地礼物资源
        val name = mGiftInfo?.code ?: ""
        val giftUri = CustomUtils.getGiftResIdById(name)


        // 加载礼物图片
        GlideUtils.load(
            view =binding.ivGift,
            url = giftUri,
            placeholder = R.drawable.gift_placehold,
            error = R.drawable.gift_placehold,
        )

        // 发送中
        binding.progressSending.visibility = if (message.status == MessageStatus.SENDING) View.VISIBLE else View.GONE

        // 如果是发送失败的礼物，显示失败标志
        binding.ivFail.visibility = if (message.status == MessageStatus.FAILED) View.VISIBLE else View.GONE
    }

    override fun updateStatus(status: MessageStatus) {

        // 根据消息状态显示状态图标
        when (status) {
            MessageStatus.SENDING -> {
                binding.progressSending.visibility = View.VISIBLE
                binding.ivFail.visibility = View.GONE
            }
            MessageStatus.SENT -> {
                binding.progressSending.visibility = View.GONE
                binding.ivFail.visibility = View.GONE
            }
            MessageStatus.FAILED -> {
                binding.progressSending.visibility = View.GONE
                binding.ivFail.visibility = View.VISIBLE
            }
            else -> {

            }
        }
    }

    private fun fillGiftNum(message: ChatMessageEntity){
        // text
        binding.tvGiftEndMessage.text = if (message.status == MessageStatus.FAILED) {
            binding.root.context.getString(R.string.send_gift_msg_fail_suffix)
        } else {
            binding.root.context.getString(R.string.send_gift_msg_suffix, "${if(message.mediaDuration < 1) 1 else message.mediaDuration}")
        }
    }

    override fun updateEntity(messageEntity: ChatMessageEntity) {
        this.mCurrentMessage = messageEntity
        fillGiftNum(messageEntity)
    }
}