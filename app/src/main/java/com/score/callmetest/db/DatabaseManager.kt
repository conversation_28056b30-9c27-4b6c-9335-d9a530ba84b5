package com.score.callmetest.db

import android.content.Context
import com.score.callmetest.db.repository.CallHistoryRepository
import com.score.callmetest.db.repository.ChatMessageRepository
import com.score.callmetest.db.repository.MessageListRepository
import com.score.callmetest.entity.CallHistoryEntity
import com.score.callmetest.entity.ChatMessageEntity
import com.score.callmetest.entity.MessageListEntity
import com.score.callmetest.manager.UserInfoManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 数据库管理器，提供统一的数据库操作接口
 * 该类作为数据库操作的主入口，方便后续替换Room
 */
class DatabaseManager private constructor(context: Context) : DatabaseContract {
    
    // 协程作用域，用于执行数据库操作
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 数据库实例
    private val database = AppDatabase.getInstance(context)
    
    // 仓库实例
    private val messageListRepository = MessageListRepository(database.messageListDao())
    private val callHistoryRepository = CallHistoryRepository(database.callHistoryDao())
    private val chatMessageRepository = ChatMessageRepository(database.chatMessageDao())
    
    companion object {
        @Volatile
        private var INSTANCE: DatabaseManager? = null
        
        /**
         * 获取数据库管理器实例
         * @param context 上下文
         * @return 数据库管理器实例
         */
        internal fun getInstance(context: Context): DatabaseManager {
            return INSTANCE ?: synchronized(this) {
                val instance = DatabaseManager(context)
                INSTANCE = instance
                instance
            }
        }
    }

    // <editor-folder desc="消息列表相关操作">

    /**
     * 插入一条消息列表项
     * @param messageList 消息列表项实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun insertMessageList(messageList: MessageListEntity, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                messageListRepository.insertMessageList(messageList)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to insert message list")
                callback?.invoke(false)
            }
        }
    }
    
    /**
     * 批量插入消息列表项
     * @param messageLists 消息列表项实体列表
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun insertMessageLists(messageLists: List<MessageListEntity>, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                messageListRepository.insertMessageLists(messageLists)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to insert message lists")
                callback?.invoke(false)
            }
        }
    }


    /**
     * 更新当前用户的消息列表项
     * @param messageList 消息列表项实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun updateCurrentUserMessageList(messageList: MessageListEntity, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                val updatedMessageList = messageList.copy(currentUserId = UserInfoManager.myUserInfo?.userId ?: "")
                messageListRepository.updateMessageList(updatedMessageList)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to update current user message list")
                callback?.invoke(false)
            }
        }
    }
    
    /**
     * 删除一条消息列表项
     * @param messageList 消息列表项实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun deleteMessageList(messageList: MessageListEntity, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                messageListRepository.deleteMessageList(messageList)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to delete message list")
                callback?.invoke(false)
            }
        }
    }
    
    /**
     * 根据用户ID删除一条消息列表项
     * @param userId 用户ID
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun deleteMessageListById(userId: String, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                messageListRepository.deleteMessageListById(userId)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to delete message list by ID")
                callback?.invoke(false)
            }
        }
    }


    /**
     * 获取当前用户的所有消息列表项，按是否置顶和时间排序
     * @return 消息列表项流
     */
    override fun getCurrentUserAllMessageLists(): Flow<List<MessageListEntity>> {
        val currentUserId = UserInfoManager.myUserInfo?.userId ?: ""
        return if (currentUserId.isNotEmpty()) {
            messageListRepository.getAllMessageListsByCurrentUserId(currentUserId)
        } else {
            messageListRepository.getAllMessageLists()
        }
    }
    
    /**
     * 根据用户ID获取一条消息列表项
     * @param userId 用户ID
     * @param callback 回调函数，在操作成功或失败时调用，参数为消息列表项实体或null
     */
    override fun getMessageListById(userId: String, callback: (MessageListEntity?) -> Unit) {
        scope.launch {
            try {
                val messageList = messageListRepository.getMessageListById(userId, UserInfoManager.myUserInfo?.userId?:"")
                callback(messageList)
            } catch (e: Exception) {
                Timber.e(e, "Failed to get message list by ID")
                callback(null)
            }
        }
    }
    
    /**
     * 更新未读消息数
     * @param userId 用户ID
     * @param unreadCount 未读消息数
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun updateUnreadCount(userId: String, unreadCount: Int, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                messageListRepository.updateUnreadCount(userId, unreadCount, UserInfoManager.myUserInfo?.userId?:"")
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to update unread count")
                callback?.invoke(false)
            }
        }
    }
    
    /**
     * 更新是否置顶
     * @param userId 用户ID
     * @param isPinned 是否置顶
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun updatePinStatus(userId: String, isPinned: Boolean, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                val currentUserId = UserInfoManager.myUserInfo?.userId ?: ""
                messageListRepository.updatePinStatus(userId, currentUserId,isPinned)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to update pin status")
                callback?.invoke(false)
            }
        }
    }

    /**
     * 更新状态
     * @param userId 用户ID
     * @param status 状态
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun updateStatus(userId: String, status: String, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                val currentUserId = UserInfoManager.myUserInfo?.userId ?: ""
                messageListRepository.updateStatus(userId, currentUserId,status)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to update pin status")
                callback?.invoke(false)
            }
        }
    }
    


    /**
     * 清空当前用户的所有消息列表
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun clearCurrentUserAllMessageLists(callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                val currentUserId = UserInfoManager.myUserInfo?.userId ?: ""
                if (currentUserId.isNotEmpty()) {
                    messageListRepository.clearAllMessageListsByCurrentUserId(currentUserId)
                }
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to clear current user all message lists")
                callback?.invoke(false)
            }
        }
    }

    // </editor-folder>


    // <editor-folder desc="通话历史相关操作">

    /**
     * 插入一条通话历史
     * @param callHistory 通话历史实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun insertCallHistory(callHistory: CallHistoryEntity, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                callHistoryRepository.insertCallHistory(callHistory)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to insert call history")
                callback?.invoke(false)
            }
        }
    }
    
    /**
     * 批量插入通话历史
     * @param callHistories 通话历史实体列表
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun insertCallHistories(callHistories: List<CallHistoryEntity>, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                callHistoryRepository.insertCallHistories(callHistories)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to insert call histories")
                callback?.invoke(false)
            }
        }
    }



    /**
     * 更新当前用户的通话历史
     * @param callHistory 通话历史实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun updateCurrentUserCallHistory(callHistory: CallHistoryEntity, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                val updatedCallHistory = callHistory.copy(currentUserId = UserInfoManager.myUserInfo?.userId ?: "")
                callHistoryRepository.updateCallHistory(updatedCallHistory)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to update current user call history")
                callback?.invoke(false)
            }
        }
    }
    
    /**
     * 删除一条通话历史
     * @param callHistory 通话历史实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun deleteCallHistory(callHistory: CallHistoryEntity, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                callHistoryRepository.deleteCallHistory(callHistory)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to delete call history")
                callback?.invoke(false)
            }
        }
    }
    
    /**
     * 根据ID删除一条通话历史
     * @param id 通话历史ID
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun deleteCallHistoryById(id: String, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                callHistoryRepository.deleteCallHistoryById(id)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to delete call history by ID")
                callback?.invoke(false)
            }
        }
    }
    


    /**
     * 获取当前用户的所有通话历史，按时间戳倒序排序
     * @return 通话历史流
     */
    override fun getCurrentUserAllCallHistories(): Flow<List<CallHistoryEntity>> {
        val currentUserId = UserInfoManager.myUserInfo?.userId ?: ""
        return if (currentUserId.isNotEmpty()) {
            callHistoryRepository.getAllCallHistoriesByCurrentUserId(currentUserId)
        } else {
            callHistoryRepository.getAllCallHistories()
        }
    }
    
    /**
     * 获取特定用户的通话历史，按时间戳倒序排序
     * @param userId 用户ID
     * @return 通话历史流
     */
    override fun getCallHistoriesByUserId(userId: String): Flow<List<CallHistoryEntity>> {
        return callHistoryRepository.getCallHistoriesByUserId(userId)
    }
    
    /**
     * 根据ID获取一条通话历史
     * @param id 通话历史ID
     * @param callback 回调函数，在操作成功或失败时调用，参数为通话历史实体或null
     */
    override fun getCallHistoryById(id: String, callback: (CallHistoryEntity?) -> Unit) {
        scope.launch {
            try {
                val callHistory = callHistoryRepository.getCallHistoryById(id)
                callback(callHistory)
            } catch (e: Exception) {
                Timber.e(e, "Failed to get call history by ID")
                callback(null)
            }
        }
    }



    /**
     * 清空当前用户的所有通话历史
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun clearCurrentUserAllCallHistories(callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                val currentUserId = UserInfoManager.myUserInfo?.userId ?: ""
                if (currentUserId.isNotEmpty()) {
                    callHistoryRepository.clearAllCallHistoriesByCurrentUserId(currentUserId)
                }
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to clear current user all call histories")
                callback?.invoke(false)
            }
        }
    }
   override fun clearCurrentUserDatas(callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                clearCurrentUserAllCallHistories()
                clearCurrentUserAllMessageLists()
                clearCurrentUserAllChatMessages()
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to clear all user data")
                callback?.invoke(false)
            }
        }
    }

    // </editor-folder>
    
    // <editor-folder desc="聊天消息相关操作">
    
    /**
     * 插入一条聊天消息
     * @param chatMessage 聊天消息实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun insertChatMessage(chatMessage: ChatMessageEntity, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                chatMessageRepository.insertChatMessage(chatMessage)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to insert chat message")
                callback?.invoke(false)
            }
        }
    }
    
    /**
     * 批量插入聊天消息
     * @param chatMessages 聊天消息实体列表
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun insertChatMessages(chatMessages: List<ChatMessageEntity>, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                chatMessageRepository.insertChatMessages(chatMessages)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to insert chat messages")
                callback?.invoke(false)
            }
        }
    }
    
    /**
     * 更新一条聊天消息
     * @param chatMessage 聊天消息实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun updateChatMessage(chatMessage: ChatMessageEntity, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                chatMessageRepository.updateChatMessage(chatMessage)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to update chat message")
                callback?.invoke(false)
            }
        }
    }
    
    /**
     * 删除一条聊天消息
     * @param chatMessage 聊天消息实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun deleteChatMessage(chatMessage: ChatMessageEntity, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                chatMessageRepository.deleteChatMessage(chatMessage)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to delete chat message")
                callback?.invoke(false)
            }
        }
    }
    
    /**
     * 根据消息ID删除一条聊天消息
     * @param messageId 消息ID
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun deleteChatMessageById(messageId: String, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                chatMessageRepository.deleteChatMessageById(messageId)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to delete chat message by ID")
                callback?.invoke(false)
            }
        }
    }
    
    /**
     * 获取特定用户的所有聊天消息，按时间排序
     * @param currentUserId 当前用户ID
     * @param userId 用户ID
     * @return 聊天消息流
     */
    override fun getChatMessagesByUserId(currentUserId: String, userId: String,callback: (List<ChatMessageEntity>) -> Unit){
        scope.launch {
            try {
                val result = chatMessageRepository.getChatMessagesByUserId(currentUserId, userId)
                callback(result)
            } catch (e: Exception) {
                Timber.e(e, "Failed to get chat message by ID")
                callback(emptyList())
            }
        }
    }
    
    /**
     * 根据消息ID获取一条聊天消息
     * @param messageId 消息ID
     * @param callback 回调函数，在操作成功或失败时调用，参数为聊天消息实体或null
     */
    override fun getChatMessageById(messageId: String, callback: (ChatMessageEntity?) -> Unit) {
        scope.launch {
            try {
                val chatMessage = chatMessageRepository.getChatMessageById(messageId)
                callback(chatMessage)
            } catch (e: Exception) {
                Timber.e(e, "Failed to get chat message by ID")
                callback(null)
            }
        }
    }

    /**
     * 更新发送状态
     * @param messageId 消息ID
     * @param status 状态
     * @param callback 回调函数，在操作成功或失败时调用，参数为聊天消息实体或null
     */
    override fun updateSendStatus(messageId: String,status: String, callback: (Boolean) -> Unit) {
        scope.launch {
            try {
                chatMessageRepository.updateSendStatus(messageId,status)
                callback(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to get chat message by ID")
                callback(false)
            }
        }
    }
    
    /**
     * 清空当前用户与特定用户之间的所有聊天消息
     * @param currentUserId 当前用户ID
     * @param userId 用户ID
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun clearChatMessagesByUserId(currentUserId: String, userId: String, callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                chatMessageRepository.clearChatMessagesByUserId(currentUserId, userId)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to clear chat messages by user ID")
                callback?.invoke(false)
            }
        }
    }
    
    /**
     * 清空所有聊天消息
     * @param callback 回调函数，在操作成功或失败时调用
     */
    override fun clearAllChatMessages(callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                chatMessageRepository.clearAllChatMessages()
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to clear all chat messages")
                callback?.invoke(false)
            }
        }
    }

    /**
     * 清空当前用户所有聊天消息
     * @param [callback] 回调
     */
    override fun clearCurrentUserAllChatMessages(callback: ((Boolean) -> Unit)?) {
        scope.launch {
            try {
                chatMessageRepository.clearCurrentUserAllChatMessages(UserInfoManager.myUserInfo?.userId!!)
                callback?.invoke(true)
            } catch (e: Exception) {
                Timber.e(e, "Failed to clear all chat messages")
                callback?.invoke(false)
            }
        }
    }

    /**
     * 根据messageId进行分页查询特定用户的聊天消息（向前查询更早的消息）
     * @param currentUserId 当前用户ID
     * @param userId 对话用户ID
     * @param messageId 起始消息ID，查询此消息之前的消息
     * @param limit 每页数量
     * @return 聊天消息实体列表
     */
    override fun getChatMessagesBeforeMessageIdByUserId(
        currentUserId: String,
        userId: String,
        messageId: String,
        limit: Int,
        callback: (List<ChatMessageEntity>) -> Unit
    ){
        scope.launch {
            try {
                val result = chatMessageRepository.getChatMessagesBeforeMessageIdByUserId(currentUserId, userId, messageId, limit)
                callback(result)
            } catch (e: Exception) {
                Timber.e(e, "Failed to get chat message by ID")
                callback(emptyList())
            }
        }
    }

    /**
     * 获取特定用户的最新聊天消息（用于初始化分页）
     * @param currentUserId 当前用户ID
     * @param userId 对话用户ID
     * @param limit 每页数量
     * @return 聊天消息实体列表
     */
    override fun getLatestChatMessagesByUserId(
        currentUserId: String,
        userId: String,
        limit: Int,
        callback: (List<ChatMessageEntity>) -> Unit
    ){
        scope.launch {
            try {
                val result = chatMessageRepository.getLatestChatMessagesByUserId(currentUserId, userId, limit)
                callback(result)
            } catch (e: Exception) {
                Timber.e(e, "Failed to get chat message by ID")
                callback(emptyList())
            }
        }
    }

    // </editor-folder>
}