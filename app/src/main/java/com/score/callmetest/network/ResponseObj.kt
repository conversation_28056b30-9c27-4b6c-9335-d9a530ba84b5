package com.score.callmetest.network

import android.os.Parcelable
import com.score.callmetest.CallStatus
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonElement

@Serializable
data class DecryptedAppConfig(
    val rvsta: String? = null,
    val ver: String? = null,
    val items: List<ConfigItem>? = null,
    val riskControlInfoConfig: RiskControlInfoConfig? = null
)

@Serializable
data class ConfigItem(
    val data: JsonElement? = null,
    val name: String? = null
)

@Serializable
data class RiskControlInfoConfig(
    val k_interval: Int? = null,
    val k_factor: String? = null,
    val k_factor_num: String? = null
)

@Serializable
data class LoginData(
    val token: String? = null,
    val isFirstRegister: Boolean? = null,
    val userInfo: UserInfo? = null,
    val aaaa: Int? = null
)

/**
 * 用户信息数据类
 */
@Serializable
@Parcelize
data class UserInfo(
    val about: String? = null, // 个性签名
    val age: Int? = null, // 年龄
    val analysisLanguage: String? = null,
    val auditStatus: Int? = null,
    var availableCoins: Int = 0, // 可用金币
    val avatar: String? = null, // 头像
    val avatarMiddleThumbUrl: String? = null, // 头像中等缩略图
    val avatarStatus: Int? = null,
    val avatarThumbUrl: String? = null, // 头像缩略图
    val avatarUrl: String? = null, // 头像URL
    val birthday: String? = null, // 生日
    val broadcasterType: Int? = null,
    val clubAvatar: String? = null,
    val clubAvatarMiddleThumbUrl: String? = null,
    val clubAvatarThumbUrl: String? = null,
    val clubAvatarUrl: String? = null,
    val country: String? = null, // 国家
    val createTime: Long? = null,
    val email: String? = null, // 邮箱
    val favors: String? = null, // 偏好
    val followNum: Int? = null,   //粉丝数量
    val followingNum: Int? = null,  //关注用户数量
    val gender: Int? = null, // 性别
    val grade: Int? = null,
    val hasEquity: Boolean? = null,
    val hasIllegalAvatar: Int? = null,
    val horizontalAvatar: String? = null,
    val horizontalAvatarMiddleThumbUrl: String? = null,
    val horizontalAvatarThumbUrl: String? = null,
    val horizontalAvatarUrl: String? = null,
    val isAnswer: Boolean = false, // 是否接听机器人
    var isBlock: Boolean = false, // 是否被block
    val isClub: Boolean = false,
    val isFakeBroadcaster: Boolean = false,
    var isFriend: Boolean = false, // 是否follow,如果这个字段返回数据里面没有，默认是false
    val isGreenMode: Boolean = false,
    val isHavePassword: Boolean = false, // 是否设置过密码
    val isInternal: Boolean? = false, // 是否内部用户
    val isMultiple: Boolean? = false,
    val isRealFriend: Boolean? = false,
    val isRecharge: Boolean? = false, // 是否充值
    val isReview: Boolean? = false,
    val isShowReviewSupplementTips: Boolean? = false, // 是否显示审核补充提示
    val isSpecialFollow: Boolean? = false, // 是否特别关注
    var isSwitchNotDisturbCall: Boolean? = false, // 是否打开通话免打扰
    var isSwitchNotDisturbIm: Boolean? = false, // 是否打开IM免打扰
    val isVip: Boolean? = false, // 是否Vip
    val language: String? = null, // 语言
    val level: Int? = null, // 用户等级
    val loginPkgName: String? = null,
    val mediaList: List<MediaResp>? = null, // 照片
    val mysteriousInfo: MysteriousInfo? = null,
    val nickname: String? = null, // 昵称
    val praiseNum: Int? = null,
    val registerCountry: String? = null, // 注册国家
    val registerPkgName: String? = null,
    val rongcloudToken: String? = null,
    val tagDetails: List<TagDetail>? = null, // 用户标签详情
    val tagsList: List<String>? = null, // 用户标签
    val unitPrice: Int? = null, // 通话价格
    val userId: String? = null, // 用户Id
    val userType: Int? = null, // 用户类型 // 1 是用户 2 是主播
    val vipExpiryTime: String? = null, // Vip到期时间
    val vipUnitPrice: Int? = null // vip通话价格
) : Parcelable

fun UserInfo.toBroadcasterModel(): BroadcasterModel {
    return BroadcasterModel(
        userId = this.userId!!,
        avatarMapPath = this.avatar,
        avatarThumbUrl = this.avatarThumbUrl,
        nickname = this.nickname,
        countdown = null, // 倒计时,  user里没有
        gender = this.gender,
        callCoins = this.unitPrice,
        videoMapPaths = null // 不需要
    )
}

@Serializable
@Parcelize
data class MysteriousInfo(
    val effective: Boolean? = null
) : Parcelable

@Serializable
@Parcelize
data class TagDetail(
    val tag: String? = null,
    val tagColor: String? = null,
    val tagTip: String? = null
) : Parcelable

/**
 * 媒体资源数据类
 */
@Serializable
@Parcelize
data class MediaResp(
    val coins: Int? = null, // 金币
    val isLock: Boolean? = null, // 是否加锁
    val mediaId: String? = null, // 媒体ID
    val mediaPath: String? = null, // 媒体路径
    val mediaType: String? = null, // 媒体类型
    val mediaUrl: String? = null, // 媒体URL
    val middleThumbUrl: String? = null, // 中等缩略图
    val sort: Int? = null, // 排序
    val thumbUrl: String? = null, // 缩略图
    val userId: String? = null // 用户ID
) : Parcelable

@Serializable
data class AppConfigData(
    val sphinx: String? = null,     // k1
    val nickname: String? = null,
    val tau: String? = null,        // k3
    val equinox: String? = null,    // k2
    val strategy: String? = null,
    val isWebApp: String? = null,
    val theta: String? = null,      //k4
    val userId: String? = null
)

// 创建通话频道接口返回值
@Serializable
data class CreateChannelResponse(
    val broadcasterMaskConfig: BroadcasterMaskConfigModel? = null, // 主播面具配置
    val broadcasterUserName: String? = null, // 主播名称
    val callFreeSeconds: Int? = null, // 免费时长
    val channelName: String? = null, // 频道名称
    val chooseVideoSdk: Int? = null, // 选用的视频SDK
    val discountedPrice: Int? = null, // 主播收入单价
    val duration: Int? = null, // 视频时长
    val fromUserId: String? = null, // 发起用户id
    val isGreenMode: Boolean? = null, // 是否绿色模式
    val rtcToken: String? = null, // 声网视频token
    val toUserId: String? = null, // 接收用户id
    val unitPrice: Int? = null, // 用户通话单价
    val videoFileUrl: String? = null, // 视频文件Url
    val videoPlayMode: String? = null // 视频播放模式（stream,player）
)

/**
 * 单条记录的数据类
 */
@Serializable
data class BroadcastRecord(
    val broadcasterId: Long,  //主播ID
    val broadcasterIdAvatar: String,  //主播头像
    val broadcasterName: String,  //主播名字
    val channelName: String,  //频道名称
    val createTime: Long,  // 创建时间
    val duration: Long,    // 通话时间
    val status: Int        // 状态
)

/**
 * 查询用户最近通话记录返回参数
 */
@Serializable
data class LatelyRecordResponseObj(
    val records: List<BroadcastRecord>
)


/**
 * 创建充值订单接口返回值
 */
@Serializable
data class CreateRechargeResponse(
    val goodsCode: String? = null, // 商品编号
    val goodsName: String? = null, // 商品名称
    val orderNo: String? = null, // 支付订单号
    val paidAmount: Double? = null, // 实付金额
    val paidCurrency: String? = null, // 实付币种
    val payAmount: Double? = null, // 支付金额(美金)
    val requestUrl: String? = null, // 跳转地址
    val tradeNo: String? = null, // 平台订单号
    var createTime: Long? = null // 创建事件---后台没有返回，自己加的
)


/**
 * 商品信息数据类
 * {
 * 		"goodsId": "a4c34a0a-1c3e-4d13-955e-9ec163464631",
 * 		"code": "263002",
 * 		"icon": "1",
 * 		"type": "0",
 * 		"subType": 0,
 * 		"thirdType": "default",
 * 		"name": "499 Coins",
 * 		"discount": 0.00,
 * 		"originalPrice": 4.99,
 * 		"price": 4.99,
 * 		"exchangeCoin": 499,
 * 		"originalExchangeCoin": 499,
 * 		"originalPriceRupee": 500.00,
 * 		"priceRupee": 500.00,
 * 		"localPaymentPriceRupee": 470.00,
 * 		"isPromotion": false,
 * 		"isForNew": false,
 * 		"localPayOriginalPrice": 500,
 * 		"localPayPrice": 470,
 * 		"sortDesc": 0,
 * 		"extraGainCoin": 0
 * 	}
 */
@Serializable
data class GoodsInfo(
    val capableRechargeNum: Int? = null, // 能够充值次数
    val code: String? = null, // 商品编号
    val discount: Double? = null, // 折扣
    val exchangeCoin: Int? = null, // 兑换金币数
    val extraCoin: Int? = null, // 额外的金币数量
    val extraCoinPercent: Int? = null, // 额外的金币比例
    val goodsId: String? = null, // 商品id
    val icon: String? = null, // 商品图标
    val invitationId: String? = null, // 邀请链接id
    val isPromotion: Boolean? = null, // 是否促销
    val originalCode: String? = null, // 升级消费的商品code
    val originalPrice: Double? = null, // 原价
    val originalPriceRupee: Double? = null, // 原价(卢比)
    val price: Double? = null, // 当前价格
    val priceRupee: Double? = null, // 当前价格(卢比)
    val rechargeNum: Int? = null, // 充值次数
    val remainMilliseconds: Long? = null, // 剩余毫秒秒数
    val surplusMillisecond: Long? = null, // 剩余毫秒数
    val tags: String? = null, // 商品标签
    val type: String? = null, // 商品类型  //todo 暂时不知道后端规定，自定义0-普通商品；1-促销商品；2-活动商品 ；3-订阅商品
    val validity: Int? = null, // 订阅有效期
    val validityUnit: String? = null, // 订阅有效期单位
    // 兼容后端其它字段
    val name: String? = null, // 商品名称
    val subType: Int? = null, // 商品子类型,0:消耗、1:黄金订阅、2:白金订阅、3:钻石订阅、4:用户权益
    val thirdType: String? = null, // 商品三级分类,CoinGoodsThirdTypeEnum
    val originalExchangeCoin: Int? = null, // 原兑换金币数
    val localPaymentPriceRupee: Double? = null, // 本地支付价格(卢比)
    val isForNew: Boolean? = null, // 是否为新用户专享
    val localPayOriginalPrice: Double? = null, // 本地支付原价
    val localPayPrice: Double? = null, // 本地支付当前价格
    val sortDesc: Int? = null, // 降序排序
    val extraGainCoin: Int? = null, // 额外获得的金币（仅展示作用）
    //活动弹窗需要的字段
    val activityName: String? = null,  //活动名称
    val activitySmallPic: String? = null,  // 活动小图片
    val activityPic: String? = null,  //活动图片
    val thirdpartyCoinPercent: Int? = null    // 第三方金币比例
)


/**
 * 查询充值结果接口返回值
 */
@Serializable
data class RechargeSearchResponse(
    val orderNo: String? = null, // 支付订单号
    val status: Int? = null // 充值状态(1-充值中 2-充值成功 3-充值失败)
)


/**
 * 标签数据类
 * @param subTagList 子标签集合
 * @param tagName 标签名称
 */
@Serializable
data class WallBroadcasterTag(
    val tagName: String? = null, // 标签名称
    val subTagList: List<String>? = null, // 子标签集合
    val showTagName: String? = null,  //展示标签名称
    val subTagInitIndex: Int? = null   // 起始地址
)

/**
 * 标签数据类
 * @param subTagList 子标签集合
 * @param tagName 标签名称
 */
@Serializable
data class Tag(
    val subTagList: List<String>? = null, // 子标签集合
    val tagName: String? = null // 标签名称
)

/**
 * 获取用户Club服务响应数据类
 * @param about 介绍
 * @param code 对应服务code
 * @param name 名称
 */
@Serializable
data class ClubServiceResponse(
    val about: String? = null, // 介绍
    val code: String? = null, // 对应服务code
    val name: String? = null // 名称
)

/**
 * 性别匹配金币数据类
 */
@Serializable
data class GenderMatchCoin(
    val bothCoins: Int? = null,
    val femaleCoins: Int? = null,
    val maleCoins: Int? = null
)

/**
 * 性别匹配金币数据类
 */
@Serializable
data class FlashChatConfig(
    val isFreeCall: Boolean? = null,
    val isSwitch: Boolean? = null,
    val residueFreeCallTimes: Int? = 0
)


/**
 * 用户邀请实体数据类
 * @param popUpBottom 邀请弹框底部内容
 * @param popUpContent 邀请弹框内容
 * @param popUpTitle 邀请弹框标题
 * @param shareContent 邀请分享内容
 * @param tipsContent 邀请提示内容
 * @param tipsTitle 邀请提示标题
 */
@Serializable
data class UserInvitation(
    val popUpBottom: String? = null, // 邀请弹框底部内容
    val popUpContent: String? = null, // 邀请弹框内容
    val popUpTitle: String? = null, // 邀请弹框标题
    val shareContent: String? = null, // 邀请分享内容
    val tipsContent: String? = null, // 邀请提示内容
    val tipsTitle: String? = null // 邀请提示标题
)

/**
 * 下发的参数配置返回值
 */
@Serializable
data class StrategyConfig(
    val activityCategory: String? = null, // 活动分类名称
    val activityCategoryImageUrl: String? = null, // 活动分类图片Url
    val broadcasterFollowOfficialUserIds: List<String>? = null, // 主播关注官方账号集合
    val broadcasterWallRegions: List<String>? = null, // 主播墙国家筛选列表
    val broadcasterWallTagList: List<WallBroadcasterTag>? = null, // 主播墙标签集合(用户策略标签表)
    val broadcasterWallTags: List<String>? = null, // 主播墙标签集合
    val clubSubTags: List<ClubServiceResponse>? = null, // club子标签
    val freeUserCallStaySecond: String? = null, // 免费用户停留时长触发call（秒）
    val freeUserImStaySecond: String? = null, // 免费用户停留时长触发im（秒）
    val genderMatchCoin: GenderMatchCoin? = null, // 性别匹配的Coins
    val flashChatConfig: FlashChatConfig? = null, // 匹配的配置项
    val imIncentiveBlacklistUserIds: List<String>? = null, // IM激励主播黑名单
    val imSessionBalance: Int? = null, // IM会话剩余数量
    val imSessionBroadcasterIds: List<Int>? = null,
    val initTab: Int? = null, // 初始化的tab是哪个
    val isAutoAccept: Boolean? = null, // 是否自动accept
    val isDisplayNotDisturbCall: Boolean? = null, // 通话免打扰开关显隐
    val isDisplayNotDisturbIm: Boolean? = null, // IM免打扰开关显隐
    val isMaskOpen: Boolean? = null, // 是否开启面具功能
    val isMatchCallFree: Boolean? = null, // 是否免费通话
    val isNewTppUsable: Boolean? = null, // 是否启用h5第三方充值，不启用=false，启用=true
    val isRandomUploadPaidEvents: Boolean? = null, // 是否随机上传付费事件
    val isReviewPkg: Boolean = false, // 是否处于审核模式
    val isShowBroadcasterRank: Boolean? = null, // 是否显示主播排行榜
    val isShowDeletedButton: Boolean? = null, // 是否显示删除按钮
    val isShowFlowInfo: Boolean? = null, // 是否显示信息流
    val isShowLP: Boolean? = null, // 是否显示本地支付选项
    val isShowMatch: Boolean? = null, // 是否显示匹配，不启用=false，启用=true
    val isShowMatchGender: Boolean? = null, // 是否显示性别匹配
    val isShowRookieGuide: Boolean? = null, // 主播新手任务开关
    val isSwitchClub: Boolean? = null, // Club开关
    val isSwitchIMIncentive: Boolean? = null, // IM激励开关
    val isSwitchIMLimit: Boolean? = null, // IM限制策略开关
    val isSwitchOneKeyFollow: Boolean? = null, // 一键关注开关
    val isSwitchStrongGuide: Boolean? = null, // 强化引导开关
    val lpDiscount: Int? = null, // 本地支付赠送比例（单位：%）
    val lpPromotionDiscount: Int? = null, // 本地支付促销商品赠送金币比例（单位：%）
    val officialBlacklistUserIds: List<String>? = null, // 官方账号黑名单集合
    val payChannels: List<String>? = null, // 支付通道集合
    val rechargeUserCallStaySecond: String? = null, // 付费用户停留时长触发call（秒）
    val rechargeUserImStaySecond: String? = null, // 付费用户停留时长触发im（秒）
    val reviewOfficialBlacklistUserIds: List<String>? = null, // 审核模式官方账号黑名单集合
    val tabType: Int? = null, // 底部tab类型
    val topOfficialUserIds: List<String>? = null, // 置顶官方账号集合
    val userInvitation: UserInvitation? = null, // 用户邀请内容
    val userWallCategorys: List<String>? = null, // 用户墙分类
    val videoStreamCategory: List<String>? = null, // 视频流分类集合
    val userServiceAccountId: String? = null // 官方客服账号id
)


/**
 * 主播墙列表项数据类
 */
@Serializable
data class BroadcasterWallItem(
    val activityTagUrl: String? = null, // 活动标签Url
    val age: Int? = null, // 年龄
    val avatar: String? = null, // 头像
    val avatarMapPath: String? = null, // 头像相对路径
    val callCoins: Int? = null, // 通话价格
    val country: String? = null, // 国家
    val followNum: Int? = null, // follow数量
    val gender: Int? = null, // 性别
    val nickname: String? = null, // 昵称
    val status: String? = null, // 状态
    val unit: String? = null, // 价格单位
    val userId: Int? = null, // 用户Id
    val videoMapPaths: List<String>? = null // 视频相对路径集合
)

/**
 * 主播额外信息返回值
 */
@Serializable
data class BroadcasterExtraInfo(
    val giftList: List<String>? = null, // 礼物标签
    val labelsList: List<String>? = null // 主播标签
)


/**
 * 获取礼物
 */
@Serializable
@Parcelize
data class GetGiftCountResponse(
    val activityGiftNum: List<GetGiftCountItem>? = null,
    val normalGiftNum: List<GetGiftCountItem>? = null,
    val specialEffectsGiftNum: List<GetGiftCountItem>? = null
) : Parcelable


/**
 * 获取礼物Item
 */
@Serializable
@Parcelize
data class GetGiftCountItem(
    val code: String? = null,
    val num: Int? = 0,
    val url: String? = null
) : Parcelable


/**
 * 赠送礼物返回值
 */
@Serializable
data class GiveGiftResponse(
    val coins: Int? = null // 剩余金币
)


/**
 * 礼物信息数据类
 */
@Parcelize
@Serializable
data class GiftInfo(
    val code: String? = null, // 礼物编号
    val coinPrice: Double? = null, // 金币价格
    val type: Int? = null, // 礼物类型 1-普通礼物 2-特殊礼物
    val giftDesc: String? = null, // 说明
    val iconPath: String? = null, // 图标路径
    val iconThumbPath: String? = null, // 图标缩略图
    val sortNo: Int? = null // 排序(编号越大，排序越前)
) : Parcelable


/**
 * 推荐主播实体数据类
 */
@Serializable
data class RecommendedBroadcaster(
    val avatarMapPath: String? = null, // 头像相路径
    val avatarThumbUrl: String? = null, // 头像
    val broadcasterId: String? = null, // 主播Id
    val broadcasterName: String? = null, // 主播名称
    val countdown: Int? = null, // 倒计时（毫秒）
    val gender: Int? = null, // 性别
    val unitPrice: Int? = null, // 通话价格
    val videoMapPaths: List<String>? = null // 视频相对路径集合
)

fun RecommendedBroadcaster.toBroadcasterModel(): BroadcasterModel {
    return BroadcasterModel(
        userId = this.broadcasterId!!,
        avatarMapPath = this.avatarMapPath,
        avatarThumbUrl = this.avatarThumbUrl,
        nickname = this.broadcasterName,
        countdown = this.countdown,
        gender = this.gender,
        callCoins = unitPrice,
        videoMapPaths = this.videoMapPaths
    )
}

@Serializable
@Parcelize
data class BroadcasterModel(
    val userId: String, // 主播ID
    val nickname: String? = null, // 昵称
    val avatar: String? = null, // 头像
    val avatarMapPath: String? = null, // 头像相对路径
    val gender: Int? = null, // 性别
    val age: Int? = null, // 年龄
    val country: String? = null, // 国家
    var status: String = CallStatus.OFFLINE, // 在线状态
    val callCoins: Int? = 0, // 通话价格
    val unit: String? = "min", // 价格单位
    val videoMapPaths: List<String>? = null, // 视频相对路径集合
    val imageMapPaths: List<String>? = null, // 图片相对路径集合
    val followNum: Int? = 0, // follow数量
    val isFriend: Boolean? = false, // 是否是好友
    val isMultiple: Boolean? = null, // 是否多人
    val about: String? = null, // 个性签名
    val grade: Int? = null, // 等级
    val analysisLanguage: String? = null, // 分析语言
    val registerCountry: String? = null, // 注册国家
    val isFakeBroadcaster: Boolean? = null, // 是否假主播
    val isShowLowPrice: Boolean? = null, // 是否显示低价
    val isSignBroadcaster: Boolean? = null, // 是否签约主播
    val showRoomVersion: Int? = null, // 房间版本
    val broadcasterType: Int? = null, // 主播类型
    val isAnswer: Boolean? = false, // 是否接听机器人
    val isLandScreen: Boolean? = null, // 是否横屏
    val prioritizeDisplayVideo: Boolean? = null, // 优先显示视频
    // 兼容性字段
    val avatarThumbUrl: String? = null, // 头像缩略图（兼容旧版本）
    val isVip: Boolean = false, // 是否是VIP（兼容旧版本）
    val countdown: Int? = null, // 倒计时（毫秒）
) : Parcelable

/**
 * 获取用户通话结果响应数据类
 */
@Serializable
data class CallResult(
    val badTagList: List<String>? = null, // 差评标签列表
    val broadcasterId: String? = null, // 主播Id
    val broadcasterName: String? = null, // 主播名称
    val channelName: String? = null, // 频道名称
    val duration: Int? = null, // 通话时长(秒)
    val isClubService: Boolean? = null, // 是否为Club服务
    val recommendList: List<RecommendedBroadcaster>? = null, // 推荐主播列表
    val tagList: List<String>? = null // 好评标签列表
)

/**
 * 查询用户头像响应数据类
 * @param avatar 头像相对路径
 * @param remoteUrl 头像Url
 * @param userId 用户Id
 */
@Serializable
data class AvatarSearchResponse(
    val avatar: String? = null, // 头像相对路径
    val remoteUrl: String? = null, // 头像Url
    val userId: Int? = null // 用户Id
)

/**
 * 获取用户头像和昵称响应数据类
 * @param about 个性签名
 * @param age 年龄
 * @param avatar 头像
 * @param avatarMiddleThumbUrl 头像middle缩略图url
 * @param avatarThumbUrl 头像缩略图url
 * @param avatarUrl 头像url
 * @param birthday 生日
 * @param country 国家
 * @param ext 扩展字段
 * @param favors 偏好
 * @param gender 性别
 * @param isVip 是否Vip
 * @param language 语言
 * @param level 用户等级
 * @param nickname 昵称
 * @param registerPkgName 注册包名
 * @param registerUtmSource registerUtmSource
 * @param sourceType cherry用户来源
 * @param spStrategy spStrategy
 * @param unitPrice 主播单位分钟价格
 * @param userId 用户id
 * @param userType 用户类型
 * @param vipExpiryTime Vip到期时间
 */
@Serializable
data class UserProfileResponse(
    val about: String? = null, // 个性签名
    val age: Int? = null, // 年龄
    val avatar: String? = null, // 头像
    val avatarMiddleThumbUrl: String? = null, // 头像middle缩略图url
    val avatarThumbUrl: String? = null, // 头像缩略图url
    val avatarUrl: String? = null, // 头像url
    val birthday: String? = null, // 生日
    val country: String? = null, // 国家
    val ext: String? = null, // 扩展字段
    val favors: String? = null, // 偏好
    val gender: Int? = null, // 性别
    val isVip: Boolean? = null, // 是否Vip
    val language: String? = null, // 语言
    val level: Int? = null, // 用户等级
    val nickname: String? = null, // 昵称
    val registerPkgName: String? = null, // 注册包名
    val registerUtmSource: String? = null, // registerUtmSource
    val sourceType: Int? = null, // cherry用户来源，1-在原包的cherry用户，2-未输入邀请码的cherry用户，3-输入了邀请码的cherry用户
    val spStrategy: Boolean? = null, // spStrategy
    val unitPrice: Int? = null, // 主播单位分钟价格
    val userId: Int? = null, // 用户id
    val userType: Int? = null, // 用户类型，1是用户 2是主播
    val vipExpiryTime: Int? = null // Vip到期时间
)

/**
 * 领取赠送金币响应数据类
 * @param bottomDocument 底部文案
 * @param coins 赠送金币
 * @param giftCoinJump 新注册送金币,领取按钮跳转,0-无,1-跳转定向女匹配,2-跳转both匹配
 * @param title 标题
 */
@Serializable
data class PresentedCoinsResponse(
    val bottomDocument: String? = null, // 底部文案
    val coins: Int? = null, // 赠送金币
    val giftCoinJump: Int? = null, // 新注册送金币,领取按钮跳转,0-无,1-跳转定向女匹配,2-跳转both匹配
    val title: String? = null // 标题
)

/**
 * 获取oss上传权限响应数据类
 * @param accessKeyId 访问身份验证中用到用户标识
 * @param callback 上传成功后的回调设置
 * @param dir 上传文件夹路径前缀
 * @param expire 过期时间
 * @param host oss对外服务的访问域名
 * @param policy 用户表单上传的策略,经过base64编码过的字符串
 * @param signature 对policy签名后的字符串
 */
@Serializable
data class OssPolicyResponse(
    val accessKeyId: String? = null, // 访问身份验证中用到用户标识
    val callback: String? = null, // 上传成功后的回调设置
    val dir: String? = null, // 上传文件夹路径前缀
    val expire: Long? = null, // 过期时间
    val host: String? = null, // oss对外服务的访问域名
    val policy: String? = null, // 用户表单上传的策略,经过base64编码过的字符串
    val signature: String? = null // 对policy签名后的字符串
)

/**
 * 更新头像响应数据类
 * @param coins 金币数
 * @param isLock 是否锁定
 * @param mediaId 媒体ID
 * @param mediaPath 媒体路径
 * @param mediaType 媒体类型
 * @param mediaUrl 媒体URL
 * @param middleThumbUrl 中等缩略图URL
 * @param sort 排序
 * @param thumbUrl 缩略图URL
 * @param userId 用户ID
 */
@Serializable
data class UpdateAvatarResponse(
    val coins: Int? = null, // 金币数
    val isLock: Boolean? = null, // 是否锁定
    val mediaId: String? = null, // 媒体ID
    val mediaPath: String? = null, // 媒体路径
    val mediaType: String? = null, // 媒体类型
    val mediaUrl: String? = null, // 媒体URL
    val middleThumbUrl: String? = null, // 中等缩略图URL
    val sort: Int? = null, // 排序
    val thumbUrl: String? = null, // 缩略图URL
    val userId: String? = null // 用户ID
)

/**
 * 更新媒体资源响应数据类
 * 与UpdateAvatarResponse结构相同，所以直接复用
 */
typealias UpdateMediaResponse = UpdateAvatarResponse

/**
 * 主播排行榜数据模型
 * @param avatar 头像
 * @param avatarMapPath 头像相对路径
 * @param broadcasterId 主播ID
 * @param broadcasterName 主播名称
 * @param broadcasterOnlineTime 主播在线时长
 * @param guildId 公会ID
 * @param guildName 公会名称
 * @param nickname 昵称
 * @param sort 排序
 * @param totalIncomeCoins 总收入金币
 * @param userId 用户Id
 */
@Serializable
data class BroadcasterRankModel(
    val avatar: String? = null, // 头像
    val avatarMapPath: String? = null, // 头像相对路径
    val broadcasterId: Int? = null, // 主播ID
    val broadcasterName: String? = null, // 主播名称
    val broadcasterOnlineTime: Int? = null, // 主播在线时长
    val guildId: Int? = null, // 公会ID
    val guildName: String? = null, // 公会名称
    val nickname: String? = null, // 昵称
    val sort: Int? = null, // 排序
    val totalIncomeCoins: Int? = null, // 总收入金币
    val userId: Int? = null // 用户Id
)

/**
 * 主播排行榜查询响应数据类
 * @param monthName 月份
 * @param rankData 排行榜数据
 * @param sortNo 当前排名
 */
@Serializable
data class BroadcasterRankResponse(
    val monthName: String? = null, // 月份
    val rankData: List<BroadcasterRankModel>? = null, // 排行榜数据
    val sortNo: String? = null // 当前排名
)

/**
 * 用户排行榜数据模型
 * @param avatar 头像
 * @param avatarMapPath 头像相对路径
 * @param nickname 昵称
 * @param sort 排序
 * @param userId 用户Id
 */
@Serializable
data class UserRankModel(
    val avatar: String? = null, // 头像
    val avatarMapPath: String? = null, // 头像相对路径
    val nickname: String? = null, // 昵称
    val sort: Int? = null, // 排序
    val userId: Int? = null // 用户Id
)

/**
 * 用户排行榜查询响应数据类
 * @param monthName 月份
 * @param rankData 排行榜数据
 * @param sortNo 当前排名
 */
@Serializable
data class UserRankResponse(
    val monthName: String? = null, // 月份
    val rankData: List<UserRankModel>? = null, // 排行榜数据
    val sortNo: String? = null // 当前排名
)

/**
 * 屏蔽列表项数据类
 * @param age 年龄
 * @param avatar 头像
 * @param broadcasterId 主播id
 * @param gender 性别
 * @param nickName 昵称
 * @param registerCountry 注册国家
 */
@Serializable
data class BlockListItem(
    val age: Int? = null, // 年龄
    val avatar: String? = null, // 头像
    val broadcasterId: String? = null, // 主播id
    val gender: Int? = null, // 性别
    val nickName: String? = null, // 昵称
    val registerCountry: String? = null // 注册国家
)

/**
 * IM聊天限制记录响应数据类
 * @param balance 剩余可用会话数量
 * @param broadcasterIds 已创建会话的主播Id集合
 */
@Serializable
data class ImSessionResponse(
    val balance: Int? = null, // 剩余可用会话数量
    val broadcasterIds: List<Int>? = null // 已创建会话的主播Id集合
)

/**
 * 推荐主播响应数据类
 * @param avatarMapPath 头像相路径
 * @param avatarThumbUrl 头像
 * @param broadcasterId 主播Id
 * @param broadcasterName 主播名称
 * @param countdown 倒计时（毫秒）
 * @param gender 性别
 * @param unitPrice 通话价格
 * @param videoMapPaths 视频相对路径集合
 */
@Serializable
data class RecommendedBroadcasterResponse(
    val avatarMapPath: String? = null, // 头像相路径
    val avatarThumbUrl: String? = null, // 头像
    val broadcasterId: String? = null, // 主播Id
    val broadcasterName: String? = null, // 主播名称
    val countdown: Int? = null, // 倒计时（毫秒）
    val gender: Int? = null, // 性别
    val unitPrice: Int? = null, // 通话价格
    val videoMapPaths: List<String>? = null // 视频相对路径集合
)

/**
 * 批量查询图片远程url响应
 * @param mediaPath 媒体路径
 * @param mediaUrl 媒体URL
 * @param middleThumbUrl 中等缩略图URL
 * @param thumbUrl 缩略图URL
 * @param userId 用户id
 */
@Serializable
data class MediaUrlResponse(
    val mediaPath: String? = null,
    val mediaUrl: String? = null,
    val middleThumbUrl: String? = null,
    val thumbUrl: String? = null,
    val userId: Int? = null
)

/**
 * 获取banner列表响应
 * @param bizType 业务类型： 1：三方支付，2：老虎机，3：导量强化
 * @param isFirstLottery 是否首次抽奖（老虎机特有）
 * @param jumpUrl 跳转链接
 * @param pic 图片
 * @param type 功能类型
 */
@Serializable
data class BannerInfoResponse(
    val bizType: String? = null, // 业务类型： 1：三方支付，2：老虎机，3：导量强化 12:游戏大厅
    val isFirstLottery: Boolean? = null, // 是否首次抽奖（老虎机特有）
    val jumpUrl: String? = null, // 跳转链接
    val pic: String? = null, // 图片
    val type: Int? = null // 功能类型 1:应用内打开 2:功能跳转(原生老虎机) 3:功能跳转(强化导量) 4:应用外打开(H5网页) 5:应用外打开(H5游戏,附加sign参数)
)

/**
 * 获取后置摄像头配置响应
 * @param isOpenCamera 是否已经开通摄像头
 * @param openCoins 开通金币数
 * @param openDay 开通天数
 */
@Serializable
data class RearCameraConfigResponse(
    val isOpenCamera: Boolean? = null,
    val openCoins: Int? = null,
    val openDay: Int? = null
)

/**
 * 主播面具配置模型
 */
@Serializable
data class BroadcasterMaskConfigModel(
    val countDown: Int? = null, // countDown
    val giftCode: String? = null, // giftCode
    val unLockMaskCoin: Int? = null // unLockMaskCoin
)

/**
 * FlashChat 快速匹配响应
 * @param broadcasterMaskConfig 主播面具配置
 * @param broadcasterUserName 主播名称
 * @param callFreeSeconds 免费时长
 * @param channelName 频道名称
 * @param chooseVideoSdk 选用的视频SDK
 * @param discountedPrice 主播收入单价
 * @param duration 视频时长
 * @param fromUserId 发起用户id
 * @param isGreenMode 是否绿色模式
 * @param rtcToken 声网视频token
 * @param toUserId 接收用户id
 * @param unitPrice 用户通话单价
 * @param videoFileUrl 视频文件Url
 * @param videoPlayMode 视频播放模式（stream,player）
 */
@Serializable
data class FlashChatResponse(
    val broadcasterMaskConfig: BroadcasterMaskConfigModel? = null,
    val broadcasterUserName: String? = null,
    val callFreeSeconds: Int = 20,
    val channelName: String? = null,
    val chooseVideoSdk: Int? = null,
    val discountedPrice: Int? = null,
    val duration: Int? = null,
    val fromUserId: String? = null,
    val isGreenMode: Boolean? = null,
    val rtcToken: String? = null,
    val toUserId: String? = null,
    val unitPrice: Int? = null,
    val videoFileUrl: String? = null,
    val videoPlayMode: String? = null
)

/**
 * 订阅商品响应
 */
@Serializable
data class SubscriptionItemResponse(
    val capableRechargeNum: Int? = null, // 能够充值次数
    val code: String? = null, // 商品编号
    val discount: Double? = null, // 折扣
    val exchangeCoin: Int? = null, // 兑换金币数
    val extraCoin: Int? = null, // 额外的金币数量
    val extraCoinPercent: Int? = null, // 额外的金币比例
    val goodsId: String? = null, // 商品id
    val icon: String? = null, // 商品图标
    val invitationId: String? = null, // 邀请链接id
    val isPromotion: Boolean? = null, // 是否促销
    val originalCode: String? = null, // 升级消费的商品code
    val originalPrice: Double? = null, // 原价
    val originalPriceRupee: Double? = null, // 原价(卢比)
    val price: Double? = null, // 当前价格
    val priceRupee: Double? = null, // 当前价格(卢比)
    val rechargeNum: Int? = null, // 充值次数
    val remainMilliseconds: Long? = null, // 剩余毫秒秒数
    val surplusMillisecond: Long? = null, // 剩余毫秒数
    val tags: String? = null, // 商品标签
    val type: String? = null, // 商品类型
    val validity: Int? = null, // 订阅有效期
    val validityUnit: String? = null // 订阅有效期单位
)

/**
 * IM聊天限制上报响应
 * @param balance 剩余可聊天主播的个数
 * @param broadcasterIds 已聊天的主播id列表
 */
@Serializable
data class CreateImSessionResponse(
    val balance: Int? = null, // 剩余可聊天主播的个数
    val broadcasterIds: List<Int>? = null // 已聊天的主播id列表
)

/**
 * 支付渠道列表项
 */
@Serializable
data class PayChannelItem(
    val iconUrl: String? = null, // 支付渠道图标
    val itemType: Int? = null, // 项目类型 1:推荐位 2:正常位 3: 折叠位
    val jumpType: Int? = null, // 跳转类型 0：应用内 1：应用外
    val payChannel: String? = null, // 支付渠道
    val presentCoinRatio: Int? = null, // 普通商品赠送金币比例
    val promotionPresentCoinRatio: Int? = null, // 促销商品赠送金币比例
    val recommendReason: String? = null, // 推荐理由
    val title: String? = null // 标题
)

/**
 * 支付渠道额外信息
 */
@Serializable
data class PayChannelExtra(
    val chooseChannel: String? = null, // 选中的支付渠道
    val isShowTppPopout: Boolean? = null, // 是否显示三方弹窗
    val tppJumpUrl: String? = null, // 三方跳转链接
    val tppPicUrl: String? = null, // 三方弹窗图片
    val tppPopoutContent: List<String>? = null, // 三方弹窗内容
    val tppPopoutType: Int? = null // 三方弹窗类型
)

/**
 * /coin/payChannel/getPostV2接口返回实体
 */
@Serializable
data class PayChannelResponse(
    val channelList: List<PayChannelItem>? = null, // 支付渠道列表
    val extra: PayChannelExtra? = null // 额外信息
)


/**
 * 关注、粉丝信息
 */
@Serializable
@Parcelize
data class FollowModel(
    val about: String? = null,                 // 个性签名
    val age: Int? = null,                      // 年龄
    val avatar: String? = null,                // 头像
    val avatarMiddleThumbUrl: String? = null,  // 头像middle缩略图url
    val avatarThumbUrl: String? = null,        // 头像缩略图url
    val avatarUrl: String? = null,             // 头像url
    val birthday: String? = null,              // 生日
    val country: String? = null,               // 国家
    val favors: String? = null,                // 偏好
    val gender: Int? = null,                   // 性别
    val isFollows: Boolean? = null,            // 是否关注对方
    val isGreenMode: Boolean? = null,          // 灵犀-是否绿色模式
    val isSignBroadcaster: Boolean? = null,    // 是否签约主播
    val isSpecialFollow: Boolean? = null,      // 是否特别关注
    val isVip: Boolean? = null,                // 是否Vip
    val language: String? = null,              // 语言
    val level: Int? = null,                    // 用户等级
    val mutualFlow: Boolean? = null,           // 互相关注
    val nickname: String? = null,              // 昵称
    val onlineStatus: String? = null,          // 在线状态
    val onlineStatusSort: Int? = null,         // 状态排序值
    val showRoomVersion: Int? = null,          // 直播场景
    val unitPrice: Int? = null,                // 主播单位分钟价格
    val userFollowTime: Int? = null,
    val userId: String,                        // 用户id
    val userType: Int? = null,                 // 用户类型
    val vipExpiryTime: Long? = null,            // Vip到期时间
    val vipLevel: Int? = null                  // 用户新订阅等级
) : Parcelable


/**
 * 最新特价商品返回参数
 */
@Parcelize
@Serializable
data class LastSpecialOfferResponse(
    val goodsId: String? = null, // 商品id
    val code: String? = null, // 商品编号
    val icon: String? = null, // 商品图标
    val type: String? = null, // 商品类型
    val discount: Double? = null, // 折扣
    val originalPrice: Double? = null, // 原价
    val price: Double? = null, // 当前价格
    val exchangeCoin: Int? = null, // 兑换金币数
    val originalExchangeCoin: Int? = null, // 原始兑换金币数
    val originalPriceRupee: Double? = null, // 原价(卢比)
    val priceRupee: Double? = null, // 当前价格(卢比)
    val localPaymentPriceRupee: Double? = null, // 本地支付价格(卢比)
    val isPromotion: Boolean? = null, // 是否促销
    val extraCoinPercent: Int? = null, // 额外的金币比例
    val extraCoin: Int? = null, // 额外的金币数量
    val remainMilliseconds: Long? = null, // 剩余毫秒数
    val rechargeNum: Int? = null, // 充值次数
    val capableRechargeNum: Int? = null, // 能够充值次数
    val invitationId: String? = null, // 邀请链接id
    val activityPic: String? = null, // 活动图片
    val activitySmallPic: String? = null, // 活动小图片
    val activityName: String? = null, // 活动名称
    val thirdpartyCoinPercent: Int? = null, // 第三方金币比例
    val localPayOriginalPrice: Double? = null, // 本地支付原价
    val localPayPrice: Double? = null // 本地支付价格
) : Parcelable


// <editor-fold desc="faq">

@Parcelize
@Serializable
data class FAQInfoList(
    val content: String, // 正文
    val faqInfoList: List<FaqInfo>? // FQA信息集合
) : Parcelable

// FAQ 信息类
@Parcelize
@Serializable
data class FaqInfo(
    val code: Int, // code
    val handleType: Int, //  处理类型(1： 跳转VipService, 2: 跳转提出建议页, 3： 跳转第三方充值页)
    val imageUrl: String, // 展示图片地址,
    val isViewExample: Boolean, // 是否有示例图
    val messageAnswer: MessageAnswer?, // 消息回答
    val question: String, //  问题
    val toUrl: String, // 跳转链接
    val type: Int // 类型（ 1: 消息， 2: 直接跳转)
) : Parcelable

// 消息回答类
@Parcelize
@Serializable
data class MessageAnswer(
    val answerEventHandleList: List<AnswerEventHandle>?, // 回答事件处理集合
    val content: String
) : Parcelable

// 回答事件处理类
@Parcelize
@Serializable
data class AnswerEventHandle(
    val handleType: Int, // 处理类型(1： 跳转VipService, 2: 跳转提出建议页, 3： 跳转第三方充值页)
    val matchStr: String, // 匹配字符串
    val toUrl: String // 跳转链接
) : Parcelable

// </editor-fold>

@Serializable
data class RelationsCounter(
    val followerCounter: Int, // 粉丝数
    val followingCounter: Int, // 关注数
    val pageFollowEachOtherCounter: Int // 互相关注数
)

@Serializable
data class GetPresentedCoinResp(
    val bottomDocument: String, //
    val coins: Int, //
    val title: String //
)

