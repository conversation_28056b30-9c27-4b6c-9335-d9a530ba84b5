package com.score.callmetest.util

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R

@SuppressLint("StaticFieldLeak")
object ToastUtils {
    private var toast: Toast? = null

    fun showToast(msg: String, duration: Int = Toast.LENGTH_SHORT) {
        showCustomToast(msg, duration)
    }

    fun showLongToast(msg: String) {
        showToast(msg, Toast.LENGTH_LONG)
    }

    fun showShortToast(msg: String) {
        showToast(msg, Toast.LENGTH_SHORT)
    }

    /**
     * 自定义Toast弹窗，居中弹窗
     * @param msg 显示消息
     * @param duration 显示时长 Toast.LENGTH_SHORT
     */
    fun showCustomToast(
        msg: String,
        duration: Int = Toast.LENGTH_SHORT,
    ) {
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.VANILLA_ICE_CREAM) {
            // ps. 虽然setView已经废弃，但在Android15中还能用，防止后续Android新版突然不能用
            Handler(Looper.getMainLooper()).post {
                toast?.cancel()

                // 创建自定义View
                val customView = LayoutInflater.from(CallmeApplication.context).inflate(R.layout.toast, null)
                val textView = customView.findViewById<TextView>(R.id.tv_toast)
                val imageView = customView.findViewById<ImageView>(R.id.image_view_logo)
                textView.text = msg
                textView.setTextColor(Color.WHITE)
                imageView.setImageResource(R.drawable.logo)

                customView.background = DrawableUtils.createRoundRectDrawable(
                    ContextCompat.getColor(CallmeApplication.context, R.color.toast_bg),
                    DisplayUtils.dp2pxInternalFloat(24f)
                )

                // 创建Toast实例
                toast = Toast(CallmeApplication.context).apply {
                    view = customView
                    setGravity(Gravity.CENTER, 0, 0) // 居中显示
                    this.duration = duration
                }

                toast?.show()
            }
        }else showCustomToastR(msg, duration)
    }

    @RequiresApi(Build.VERSION_CODES.R)
    private fun showCustomToastR(
        msg: String,
        duration: Int = Toast.LENGTH_SHORT,
    ) {
        Handler(Looper.getMainLooper()).post {
            toast?.cancel()

            // 创建Toast实例
            toast = Toast(CallmeApplication.context).apply {
                setText(msg)
                setGravity(Gravity.TOP or Gravity.CENTER, 0, 100) // 居中显示
                this.duration = duration
            }

            toast?.show()
        }
    }
}