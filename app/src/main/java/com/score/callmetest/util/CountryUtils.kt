package com.score.callmetest.util

import android.content.Context
import com.score.callmetest.R
import com.hbb20.CCPCountry
import com.hbb20.CountryCodePicker
import timber.log.Timber

/**
 * 国家管理工具类，包含常见国家的中英文名、区号、ISO代码、图标等信息
 */
object CountryUtils {
    data class Country(
        val zhName: String,
        val enName: String,
        val code: String, // 区号，如 +86
        val iso: String,  // ISO 2位代码，如 CN
        val iconRes: Int? = null // 国家图标资源id，可为null
    )

    // 缓存从开源库获取的国家数据
    private var cachedLibraryCountries: List<Country>? = null

    // 只列出HomeFragment中用到的国家，iconRes需在R.mipmap下定义
    private val countryList = listOf(
        Country("哥伦比亚", "Colombia", "+57", "CO", com.score.callmetest.R.drawable.co),
        Country("摩洛哥", "Morocco", "+212", "MA", com.score.callmetest.R.drawable.ma),
        Country("巴西", "Brazil", "+55", "BR", com.score.callmetest.R.drawable.br),
        Country("委内瑞拉", "Venezuela", "+58", "VE", com.score.callmetest.R.drawable.ve),
        Country("越南", "Vietnam", "+84", "VN", com.score.callmetest.R.drawable.vn),
        Country("乌克兰", "Ukraine", "+380", "UA", com.score.callmetest.R.drawable.ua),
        Country("菲律宾", "Philippines", "+63", "PH", com.score.callmetest.R.drawable.ph),
        Country("印度", "India", "+91", "IN", com.score.callmetest.R.drawable.`in`),
        Country("泰国", "Thailand", "+66", "TH", com.score.callmetest.R.drawable.th),
        Country("秘鲁", "Peru", "+51", "PE", com.score.callmetest.R.drawable.pe),
        Country("厄瓜多尔", "Ecuador", "+593", "EC", com.score.callmetest.R.drawable.ec),
        Country("土耳其", "Turkey", "+90", "TR", com.score.callmetest.R.drawable.tr),
        Country("阿根廷", "Argentina", "+54", "AR", com.score.callmetest.R.drawable.ar),
        Country("阿塞拜疆", "Azerbaijan", "+994", "AZ", com.score.callmetest.R.drawable.az),
        Country("美国", "United States", "+1", "US", com.score.callmetest.R.drawable.us),
        Country("俄罗斯", "Russia", "+7", "RU", com.score.callmetest.R.drawable.ru),
        Country("印尼", "Indonesia", "+62", "ID", com.score.callmetest.R.drawable.id),
        // 其它常见国家（无icon）
       // Country("中国", "China", "+86", "CN", null),
        Country("日本", "Japan", "+81", "JP", null),
        Country("韩国", "South Korea", "+82", "KR", null),
        Country("英国", "United Kingdom", "+44", "GB", null),
        Country("法国", "France", "+33", "FR", null),
        Country("德国", "Germany", "+49", "DE", null),
        Country("加拿大", "Canada", "+1", "CA", null),
        Country("澳大利亚", "Australia", "+61", "AU", null),
        Country("新加坡", "Singapore", "+65", "SG", null),
        Country("马来西亚", "Malaysia", "+60", "MY", null),
        Country("墨西哥", "Mexico", "+52", "MX", null),
        Country("意大利", "Italy", "+39", "IT", null)
    )

    /** 获取全部国家列表 */
    fun getAllCountries(): List<Country> = countryList

    /** 获取所有有icon的国家列表 */
    fun getAllCountriesWithIcon(): List<Country> = countryList.filter { it.iconRes != null }

    /** 通过ISO代码查找有icon国家 */
    fun getCountryIconResByIso(iso: String?): Int {
        return getCountryByIso(iso ?: "")?.iconRes ?: com.score.callmetest.R.drawable.map_language
    }

    /** 通过区号查找国家 */
    fun getCountryByCode(code: String): Country? = countryList.find { it.code == code }

    /** 通过ISO代码查找国家 */
    fun getCountryByIso(iso: String): Country? = countryList.find { it.iso.equals(iso, true) }
    /** 通过中文名查找国家 */
    fun getCountryByZhName(zhName: String): Country? = countryList.find { it.zhName == zhName }

    /** 通过英文名查找国家 */
    fun getCountryByEnName(enName: String): Country? = countryList.find { it.enName.equals(enName, true) }

    /** 获取当前国家（根据系统Locale） */
    fun getCurrentCountry(): String? {
        val locale = java.util.Locale.getDefault()
        /*// 优先用ISO代码匹配
        getCountryByIso(locale.country)?.let { return it }
        // 其次用英文名匹配
        getCountryByEnName(locale.displayCountry)?.let { return it }*/
        return locale.country
    }

    /** 通过英文名查找图标 */
    fun getIconByEnName(enName: String?): Int {
        return getCountryByEnName(enName ?: "")?.iconRes ?: R.drawable.map_language
    }

    /**
     * 通过英文名从缓存的开源库数据中查找图标
     * 优先从开源库缓存数据中查找，找不到则使用默认图标
     * 支持模糊匹配：如果完全匹配失败，则尝试匹配第一个单词
     * 例如：查询"Venezuela"可以匹配到"Venezuela, Bolivarian Republic Of"
     * 注意：如果缓存为空且没有通过initializeLibraryCountriesWithContext初始化，将返回默认图标
     */
    fun getIconByEnNameFromLibrary(enName: String?): Int {
        if (enName.isNullOrBlank()) return R.drawable.map_language

        // 从缓存的开源库数据中查找
        // 1. 首先尝试完全匹配
        var country = cachedLibraryCountries?.find {
            it.enName.equals(enName, ignoreCase = true)
        }

        // 2. 如果完全匹配失败，尝试第一个单词匹配
        if (country == null) {
            country = cachedLibraryCountries?.find { cachedCountry ->
                val cachedFirstWord = cachedCountry.enName.trim().split(" ")[0].removeSuffix(",")
                cachedFirstWord.equals(enName, ignoreCase = true)
            }
        }

        return country?.iconRes ?: R.drawable.map_language
    }

    /**
     * 通过iso从缓存的开源库数据中查找图标
     * 优先从开源库缓存数据中查找，找不到则使用默认图标
     * 注意：如果缓存为空且没有通过initializeLibraryCountriesWithContext初始化，将返回默认图标
     */
    fun getIconByIsoFromLibrary(iso: String?): Int {
        if (iso.isNullOrBlank()) return R.drawable.map_language

        // 从缓存的开源库数据中查找
        val country = cachedLibraryCountries?.find {
            it.iso.equals(iso, ignoreCase = true)
        }

        return country?.iconRes ?: R.drawable.map_language
    }


    /**
     * 初始化开源库并缓存国家数据
     * 需要传入Context以确保开源库正确加载
     * @param context Android Context，用于初始化开源库
     * @param language 语言设置，默认为英语
     */
    fun initializeLibraryCountriesWithContext(
        context: Context,
        language: CountryCodePicker.Language = CountryCodePicker.Language.ENGLISH
    ) {

        if (cachedLibraryCountries == null) {
            try {
                // 使用CountryCodePicker库获取国家数据
                val ccpCountries = CCPCountry.getLibraryMasterCountryList(context, language)
                //Timber.tag("CountryUtils").d("从CountryCodePicker库获取到 ${ccpCountries.size} 个国家/地区")

                if (ccpCountries.isEmpty()) {
                    Timber.tag("CountryUtils").w("CountryCodePicker库返回空列表")
                    cachedLibraryCountries = emptyList()
                    return
                }

                cachedLibraryCountries = ccpCountries
                    .filter { ccpCountry ->
                        // 过滤掉中国：检查英文名、本地化名称和ISO代码
                        val englishName = ccpCountry.englishName ?: ""
                        val localName = ccpCountry.name ?: ""
                        val nameCode = ccpCountry.nameCode ?: ""

                        !englishName.equals("China", ignoreCase = true) &&
                        !localName.equals("中国", ignoreCase = true) &&
                        !localName.equals("China", ignoreCase = true) &&
                        !nameCode.equals("CN", ignoreCase = true)
                    }
                    .map { ccpCountry ->
                        //Timber.tag("CountryUtils").d("缓存国家: ${ccpCountry.englishName} (${ccpCountry.nameCode}).  icon: ${ccpCountry.flagID}")
                        Country(
                            zhName = ccpCountry.name ?:  "", // 本地化名称，可能是中文
                            enName = ccpCountry.englishName ?: "", // 英文名称
                            code = "+" + ccpCountry.phoneCode, // 区号
                            iso = ccpCountry.nameCode ?: "", // ISO代码
                            iconRes = ccpCountry.flagID // 图标资源
                        )
                    }
                Timber.tag("CountryUtils").d("国家数据缓存完成，共缓存 ${cachedLibraryCountries?.size} 个国家")
            } catch (e: Exception) {
                Timber.tag("CountryUtils").e(e, "加载CountryCodePicker库时发生异常")
                cachedLibraryCountries = emptyList()
            }
        }
    }

    /**
     * 从开源库获取国家列表，过滤掉中国，只保留图标和英文名字
     * 如果已缓存则直接返回缓存数据，否则先初始化再返回
     * 注意：如果缓存为空且没有通过initializeLibraryCountriesWithContext初始化，可能返回空列表
     */
    fun getCountriesFromLibrary(): List<Country> {
        return cachedLibraryCountries ?: emptyList()
    }

    /**
     * 通过 ISO 代码获取英文名称
     * 优先从开源库缓存数据中查找，找不到则从本地数据中查找
     * @param iso ISO 2位代码，如 "IN"
     * @return 对应的英文名称，如 "India"，找不到则返回原 ISO 代码
     */
    fun getEnNameByIso(iso: String?): String {
        if (iso.isNullOrBlank()) return ""

        // 优先从开源库缓存数据中查找
        cachedLibraryCountries?.find {
            it.iso.equals(iso, ignoreCase = true)
        }?.let { return it.enName }

        // 从本地数据中查找
        getCountryByIso(iso)?.let { return it.enName }

        // 都找不到则返回原 ISO 代码
        return iso
    }

    /**
     * 通过英文名称获取 ISO 代码
     * 优先从开源库缓存数据中查找，找不到则从本地数据中查找
     * @param enName 英文名称，如 "India"
     * @return 对应的 ISO 2位代码，如 "IN"，找不到则返回原英文名称
     */
    fun getIsoByEnName(enName: String?): String {
        if (enName.isNullOrBlank()) return ""

        // 优先从开源库缓存数据中查找
        cachedLibraryCountries?.find {
            it.enName.equals(enName, ignoreCase = true)
        }?.let { return it.iso }

        // 从本地数据中查找
        getCountryByEnName(enName)?.let { return it.iso }

        // 都找不到则返回原英文名称
        return enName
    }

}