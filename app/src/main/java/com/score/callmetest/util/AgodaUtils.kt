package com.score.callmetest.util

import android.content.Context
import android.util.Log
import androidx.lifecycle.LifecycleOwner
import com.score.callmetest.manager.AppConfigManager
import com.score.callmetest.manager.LogReportManager
import com.score.callmetest.network.JoinChannelRequest
import com.score.callmetest.network.LiveCallAction
import com.score.callmetest.network.LiveCallExt
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.network.UpdateAgoraUidRequest
import io.agora.rtc2.Constants
import io.agora.rtc2.IRtcEngineEventHandler
import io.agora.rtc2.RtcEngine
import io.agora.rtc2.video.VideoCanvas
import io.agora.rtc2.video.VideoEncoderConfiguration
import kotlinx.serialization.json.contentOrNull
import kotlinx.serialization.json.jsonPrimitive

sealed class AgodaCallEvent {
    data class Error(val err: Int, val msg: String) : AgodaCallEvent()
    data class JoinChannelSuccess(val channel: String?, val uid: Int, val elapsed: Int) :
        AgodaCallEvent()

    data class UserJoined(val uid: Int, val elapsed: Int) : AgodaCallEvent()
    data class UserOffline(val uid: Int, val reason: Int) : AgodaCallEvent()
    data class UserMuteVideo(val uid: Int, val muted: Boolean) : AgodaCallEvent()
    data class UserMuteAudio(val uid: Int, val muted: Boolean) : AgodaCallEvent()
    data class ConnectionStateChanged(val state: Int, val reason: Int) : AgodaCallEvent()
    data class NetworkQuality(val uid: Int, val txQuality: Int, val rxQuality: Int) :
        AgodaCallEvent()
}

object AgodaUtils {
    private var rtcEngine: RtcEngine? = null
    private var isInitialized = false
    var remoteUid: Int = 0
    var currentChannel: String? = null

    /**
     * 全局初始化Agora，在登录成功后调用
     */
    fun initializeGlobal(context: Context) {
        val appId =
            AppConfigManager.getDecryptedAppConfig()?.items?.find { it.name == "rtck" }?.data?.jsonPrimitive?.contentOrNull

        if (appId.isNullOrEmpty()) {
            Log.w("AgodaUtils", "未获取到声网AppId，跳过初始化")
            return
        }

        if (isInitialized) {
            Log.d("AgodaUtils", "Agora已经初始化过了")
            return
        }

        try {
            rtcEngine = RtcEngine.create(
                context.applicationContext,
                appId,
                object : IRtcEngineEventHandler() {
                    var lastJoinChannel: String = ""
                    override fun onError(err: Int) {
                        Log.e("AgodaUtils", "Agora错误: " + getErrorDescription(err))
                        EventBus.post(AgodaCallEvent.Error(err, getErrorDescription(err)))
                    }

                    override fun onJoinChannelSuccess(channel: String?, uid: Int, elapsed: Int) {
                        Log.d(
                            "AgodaUtils",
                            "加入频道成功: channel=$channel, uid=$uid, elapsed=$elapsed"
                        )
                        currentChannel = channel

                        LogReportManager.reportLiveCallEvent(
                            action = LiveCallAction.JOIN_SUCCESS,
                            ext = LiveCallExt.CHATTING
                        )

                        if (channel != lastJoinChannel) {
                            lastJoinChannel = channel.toString()
                            ThreadUtils.runOnIO {
                                try {
                                    RetrofitUtils.dataRepository.joinChannel(
                                        JoinChannelRequest(
                                            channel
                                        )
                                    )
                                    RetrofitUtils.dataRepository.updateAgoraUid(
                                        UpdateAgoraUidRequest(
                                            uid.toString()
                                        )
                                    )
                                } catch (e: Exception) {

                                }
                            }
                        }

                        EventBus.post(AgodaCallEvent.JoinChannelSuccess(channel, uid, elapsed))
                    }

                    override fun onUserJoined(uid: Int, elapsed: Int) {
                        Log.d("AgodaUtils", "远端用户加入: uid=$uid, elapsed=$elapsed")
                        remoteUid = uid

                        LogReportManager.reportLiveCallEvent(
                            action = LiveCallAction.USER_JOIN,
                            ext = LiveCallExt.CHATTING
                        )
                        EventBus.post(AgodaCallEvent.UserJoined(uid, elapsed))
                    }

                    override fun onUserOffline(uid: Int, reason: Int) {
                        Log.d("AgodaUtils", "远端用户离开: uid=$uid, reason=$reason")
                        remoteUid = 0
                        currentChannel = null
                        EventBus.post(AgodaCallEvent.UserOffline(uid, reason))
                    }

                    override fun onUserMuteVideo(uid: Int, muted: Boolean) {
                        Log.d("AgodaUtils", "远端用户视频静音状态改变: uid=$uid, muted=$muted")
                        EventBus.post(AgodaCallEvent.UserMuteVideo(uid, muted))
                    }

                    override fun onUserMuteAudio(uid: Int, muted: Boolean) {
                        Log.d("AgodaUtils", "远端用户音频静音状态改变: uid=$uid, muted=$muted")
                        EventBus.post(AgodaCallEvent.UserMuteAudio(uid, muted))
                    }

                    override fun onConnectionStateChanged(state: Int, reason: Int) {
                        Log.d("AgodaUtils", "连接状态改变: state=$state, reason=$reason")
                        EventBus.post(AgodaCallEvent.ConnectionStateChanged(state, reason))
                    }

                    override fun onNetworkQuality(uid: Int, txQuality: Int, rxQuality: Int) {
                        EventBus.post(AgodaCallEvent.NetworkQuality(uid, txQuality, rxQuality))
                    }
                })

            // 设置默认的编码配置（新版SDK移除MIRROR_MODE_TYPE参数）
            rtcEngine?.setVideoEncoderConfiguration(
                VideoEncoderConfiguration(
                    VideoEncoderConfiguration.VD_640x360,
                    VideoEncoderConfiguration.FRAME_RATE.FRAME_RATE_FPS_15,
                    VideoEncoderConfiguration.STANDARD_BITRATE,
                    VideoEncoderConfiguration.ORIENTATION_MODE.ORIENTATION_MODE_ADAPTIVE
                )
            )

            // 设置频道配置 - 必须设置为通信场景，避免3分钟挂断问题
            rtcEngine?.setChannelProfile(Constants.CHANNEL_PROFILE_COMMUNICATION)


            // 视频默认禁用，你需要调用 enableVideo 开始视频流。
            enableVideo()
            isInitialized = true
            Log.d("AgodaUtils", "Agora初始化成功，已设置为通信场景")
        } catch (e: Exception) {
            Log.e("AgodaUtils", "Agora初始化失败", e)
        }
    }

    /**
     * 检查是否已初始化
     */
    fun isInitialized(): Boolean {
        return isInitialized && rtcEngine != null
    }

    fun joinChannel(token: String?, channelName: String, optionalInfo: String?, optionalUid: Int) {
        rtcEngine?.joinChannel(token, channelName, optionalInfo, optionalUid)
    }

    /**
     * 使用用户账号加入频道（推荐使用，避免3分钟挂断问题）
     */
    fun joinChannelWithUserAccount(token: String?, channelName: String, userAccount: String) {
        Log.d("AgodaUtils", "joinChannelWithUserAccount: channelName: ${channelName}")
        rtcEngine?.joinChannelWithUserAccount(token, channelName, userAccount)
    }

    fun leaveChannel() {
        rtcEngine?.leaveChannel()
    }

    fun destroy() {
        rtcEngine?.setupLocalVideo(null)
        rtcEngine?.setupRemoteVideo(null)
    }

    /**
     * 彻底释放RtcEngine资源
     */
    fun release() {
        try {
            rtcEngine?.leaveChannel()
            rtcEngine?.stopPreview()
            RtcEngine.destroy()
        } catch (e: Exception) {
            Log.e("AgodaUtils", "release error", e)
        } finally {
            rtcEngine = null
            isInitialized = false
            remoteUid = 0
            currentChannel = null
        }
    }

    fun getRtcEngine(): RtcEngine? = rtcEngine

    // 视频相关API
    fun setupLocalVideo(videoCanvas: VideoCanvas) {
        rtcEngine?.setupLocalVideo(videoCanvas)
    }

    fun setupRemoteVideo(videoCanvas: VideoCanvas) {
        rtcEngine?.setupRemoteVideo(videoCanvas)
    }

    fun startPreview() {
        rtcEngine?.startPreview()
    }

    fun stopPreview() {
        rtcEngine?.stopPreview()
    }

    fun enableVideo() {
        rtcEngine?.enableVideo()
    }

    fun disableVideo() {
        rtcEngine?.disableVideo()
    }

    fun enableLocalVideo(enabled: Boolean) {
        rtcEngine?.enableLocalVideo(enabled)
    }

    fun muteLocalVideoStream(muted: Boolean) {
        rtcEngine?.muteLocalVideoStream(muted)
    }

    fun muteRemoteVideoStream(uid: Int, muted: Boolean) {
        rtcEngine?.muteRemoteVideoStream(uid, muted)
    }

    fun muteAllRemoteVideoStreams(muted: Boolean) {
        rtcEngine?.muteAllRemoteVideoStreams(muted)
    }

    // 音频相关API
    fun enableAudio() {
        rtcEngine?.enableAudio()
    }

    fun disableAudio() {
        rtcEngine?.disableAudio()
    }

    fun enableLocalAudio(enabled: Boolean) {
        rtcEngine?.enableLocalAudio(enabled)
    }

    fun muteLocalAudioStream(muted: Boolean) {
        rtcEngine?.muteLocalAudioStream(muted)
    }

    fun muteRemoteAudioStream(uid: Int, muted: Boolean) {
        rtcEngine?.muteRemoteAudioStream(uid, muted)
    }

    fun muteAllRemoteAudioStreams(muted: Boolean) {
        rtcEngine?.muteAllRemoteAudioStreams(muted)
    }

    fun setChannelProfile(profile: Int) {
        rtcEngine?.setChannelProfile(profile)
    }

    fun enableDualStreamMode(enabled: Boolean) {
        rtcEngine?.enableDualStreamMode(enabled)
    }

    // 摄像头控制
    fun switchCamera() {
        rtcEngine?.switchCamera()
    }

    fun setCameraAutoFocusFaceModeEnabled(enabled: Boolean) {
        rtcEngine?.setCameraAutoFocusFaceModeEnabled(enabled)
    }

    // 音量控制
    fun adjustRecordingSignalVolume(volume: Int) {
        rtcEngine?.adjustRecordingSignalVolume(volume)
    }

    fun adjustPlaybackSignalVolume(volume: Int) {
        rtcEngine?.adjustPlaybackSignalVolume(volume)
    }

    // 错误处理
    fun getErrorDescription(error: Int): String {
        return RtcEngine.getErrorDescription(error)
    }

    fun observeError(owner: LifecycleOwner, onEvent: (AgodaCallEvent.Error) -> Unit) {
        EventBus.observe(owner, AgodaCallEvent.Error::class.java, onEvent = onEvent)
    }

    fun observeJoinChannelSuccess(
        owner: LifecycleOwner,
        onEvent: (AgodaCallEvent.JoinChannelSuccess) -> Unit
    ) {
        EventBus.observe(owner, AgodaCallEvent.JoinChannelSuccess::class.java, onEvent = onEvent)
    }

    fun observeUserJoined(owner: LifecycleOwner, onEvent: (AgodaCallEvent.UserJoined) -> Unit) {
        EventBus.observe(owner, AgodaCallEvent.UserJoined::class.java, onEvent = onEvent)
    }

    fun observeUserOffline(owner: LifecycleOwner, onEvent: (AgodaCallEvent.UserOffline) -> Unit) {
        EventBus.observe(owner, AgodaCallEvent.UserOffline::class.java, onEvent = onEvent)
    }

    fun observeUserMuteVideo(
        owner: LifecycleOwner,
        onEvent: (AgodaCallEvent.UserMuteVideo) -> Unit
    ) {
        EventBus.observe(owner, AgodaCallEvent.UserMuteVideo::class.java, onEvent = onEvent)
    }

    fun observeUserMuteAudio(
        owner: LifecycleOwner,
        onEvent: (AgodaCallEvent.UserMuteAudio) -> Unit
    ) {
        EventBus.observe(owner, AgodaCallEvent.UserMuteAudio::class.java, onEvent = onEvent)
    }

    fun observeConnectionStateChanged(
        owner: LifecycleOwner,
        onEvent: (AgodaCallEvent.ConnectionStateChanged) -> Unit
    ) {
        EventBus.observe(
            owner,
            AgodaCallEvent.ConnectionStateChanged::class.java,
            onEvent = onEvent
        )
    }

    fun observeNetworkQuality(
        owner: LifecycleOwner,
        onEvent: (AgodaCallEvent.NetworkQuality) -> Unit
    ) {
        EventBus.observe(owner, AgodaCallEvent.NetworkQuality::class.java, onEvent = onEvent)
    }
} 