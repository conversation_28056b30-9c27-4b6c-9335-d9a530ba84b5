package com.score.callmetest.util

import android.content.Context
import android.os.Build
import com.adjust.sdk.AdjustAttribution
import com.google.android.gms.ads.identifier.AdvertisingIdClient
import com.score.callmetest.BuildConfig
import com.score.callmetest.CallmeApplication
import com.score.callmetest.Constant
import com.score.callmetest.manager.AdjustManager
import com.score.callmetest.manager.AppConfigManager
import timber.log.Timber

object HeaderUtils {
    // 全局存储 Adjust 归因数据
    @Volatile
    var adjustAttribution: AdjustAttribution? = null

    fun buildCommonHeaders(context: Context = CallmeApplication.context): Map<String, String> {
        val headers = mutableMapOf<String, String>()
        // 基础平台信息
        headers[Constant.HEADER_PLATFORM] = Constant.HEADER_PLATFORM_VALUE
        headers[Constant.HEADER_PLATFORM_VER] = Build.VERSION.SDK_INT.toString()
        headers[Constant.HEADER_PKG] = Constant.getApplicationName()
        headers[Constant.HEADER_VER] = BuildConfig.VERSION_NAME
        headers[Constant.HEADER_MODEL] = Build.MODEL ?: ""
        headers[Constant.HEADER_DEVICE_ID] = DeviceUtils.getAndroidId()
        // 语言相关
        val lang = LocaleUtils.getSystemLanguage()
        headers[Constant.HEADER_LANG] = lang
        headers[Constant.HEADER_SYS_LAN] = lang
        headers[Constant.HEADER_DEVICE_LANG] = LanguageUtils.getSystemLanguage() ?: ""
        headers[Constant.HEADER_DEVICE_COUNTRY] = CountryUtils.getCurrentCountry() ?: ""
        // 时区
        headers[Constant.HEADER_TIME_ZONE] = TimeZoneUtils.getCurrentTimeZoneId() ?: ""
        headers[Constant.HEADER_RC_TYPE] = AppConfigManager.getConfigValue("rc_area_code") ?: ""
        // 归因相关参数
        headers[Constant.HEADER_ATTRIBUTION_SDK] = "AJ"
        headers[Constant.HEADER_ATTRIBUTION_SDK_VER] = AdjustManager.attributionSdkVer
        // 动态写入 Adjust 归因数据
        adjustAttribution?.let { attr ->
            headers[Constant.HEADER_UTM_SOURCE] = attr.network ?: ""
            headers[Constant.HEADER_AF_ADGROUP_ID] = attr.adgroup ?: ""
            headers[Constant.HEADER_AF_ADSET_ID] = attr.creative ?: ""
            headers[Constant.HEADER_CAMPAIGN_ID] = attr.campaign ?: ""
        } ?: run {
            headers[Constant.HEADER_UTM_SOURCE] = ""
            headers[Constant.HEADER_AF_ADGROUP_ID] = ""
            headers[Constant.HEADER_AF_ADSET_ID] = ""
            headers[Constant.HEADER_CAMPAIGN_ID] = ""
        }
        headers[Constant.HEADER_CAMPAIGN] = ""
        headers[Constant.HEADER_AF_ADSET] = ""
        headers[Constant.HEADER_AF_STATUS] = ""
        headers[Constant.HEADER_AF_AGENCY] = ""
        headers[Constant.HEADER_AF_CHANNEL] = ""
        // 主播标识
        headers[Constant.HEADER_IS_ANCHOR] = Constant.HEADER_IS_ANCHOR_VALUE
        // token
        val token = SharePreferenceUtil.getString(Constant.TOKEN_KEY, "")
        headers[Constant.HEADER_AUTHORIZATION] = "Bearer $token"
        try {
            headers[Constant.HEADER_AD_ID] = AdvertisingIdClient.getAdvertisingIdInfo(context).id
                ?: "00000000-0000-0000-0000-00000000000"
        } catch (e: Exception) {
            Timber.e(e)
            headers[Constant.HEADER_AD_ID] = "00000000-0000-0000-0000-00000000000"
        }
        return headers
    }
} 